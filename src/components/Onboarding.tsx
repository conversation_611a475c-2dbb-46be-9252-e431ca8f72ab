import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Loader2 } from "lucide-react";
import { loadStripe } from "@stripe/stripe-js";
import { SupabaseClient } from "@supabase/supabase-js";
import { Checkbox } from "@/components/ui/checkbox";
import { setSubscriptionCookie, getSubscriptionCookie } from "@/utils/cookieUtils";
import { getSelectedPlanType, getPriceIdForPlanType, clearSelectedPlanType, PLAN_TYPES } from "@/utils/planUtils";

interface OnboardingProps {
  isOpen: boolean;
  onClose: () => void;
  supabase: SupabaseClient;
}

export default function Onboarding({ isOpen, onClose, supabase }: OnboardingProps) {
  // Simplified state for Google auth and pricing only
  const [currentStep, setCurrentStep] = useState(1); // 1 = Google auth, 2 = pricing
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);
  const [termsAgreed, setTermsAgreed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Auto-redirect to Stripe checkout when pricing card is shown, but only if user doesn't have a plan
  useEffect(() => {
    // Check if we're showing the pricing card (isLoggedIn)
    if (isLoggedIn && currentStep === 2) {
      // Set loading state to show user something is happening
      setIsLoading(true);

      let redirectTimerCompleted = false;

      // Add a short delay to check for subscription before redirecting
      const redirectTimer = setTimeout(async () => {
        redirectTimerCompleted = true;

        try {
          // Check if user has an active subscription before redirecting
          const hasActiveSubscription = await checkUserSubscription();

          if (hasActiveSubscription) {
            // Set cookie for future reference
            setSubscriptionCookie(true);
            // Close onboarding
            onClose();
          } else {
            // Set cookie for future reference
            setSubscriptionCookie(false);
            // Redirect to Stripe
            handleSelectPlan('weekly');
          }
        } catch (error) {
          console.error('Error checking subscription status:', error);
          // If there's an error, proceed with redirect to be safe
          handleSelectPlan('weekly');
        }
      }, 1000); // 1 second delay to check subscription status (reduced from 2 seconds)

      return () => {
        if (!redirectTimerCompleted) {
          clearTimeout(redirectTimer);
        }
      };
    }
  }, [isLoggedIn, currentStep, onClose]);

  // Check for returning OAuth users
  useEffect(() => {
    if (!isOpen) return;

    const checkAuthSession = async () => {
      const { data } = await supabase.auth.getSession();

      if (data.session) {
        // User is already logged in, go to pricing card
        setIsLoggedIn(true);
        setCurrentStep(2); // Show pricing card
      }
    };

    checkAuthSession();
  }, [isOpen, supabase.auth]);

  // Handle Google sign in
  const handleGoogleSignIn = async () => {
    try {
      setIsSubmitting(true);

      // Check if there's a selected plan type to preserve through auth flow
      const selectedPlanType = getSelectedPlanType();
      let redirectUrl = window.location.origin + '?subscription_pending=true';

      // Check for no-trial parameters
      const urlParams = new URLSearchParams(window.location.search);
      const basicNoTrial = urlParams.get('basicnotrial') !== null;
      const proNoTrial = urlParams.get('pronotrial') !== null;

      // Add the plan type to the redirect URL if it exists
      if (selectedPlanType === PLAN_TYPES.BASIC || basicNoTrial) {
        // If it's a no-trial basic plan, preserve that parameter
        if (basicNoTrial) {
          redirectUrl += '&basicnotrial';
        } else {
          redirectUrl += '&basic';
        }
      } else if (selectedPlanType === PLAN_TYPES.PRO || proNoTrial) {
        // If it's a no-trial pro plan, preserve that parameter
        if (proNoTrial) {
          redirectUrl += '&pronotrial';
        } else {
          redirectUrl += '&pro';
        }
      }

      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUrl,
          queryParams: {
            // Add a query parameter to indicate that the user should be redirected to paywall
            skip_onboarding: 'true'
          }
        }
      });

      if (error) throw error;
    } catch (error: any) {
      setLoginError(error.message);
      setIsSubmitting(false);
    }
  };

  // Function to check if user has an active subscription
  const checkUserSubscription = async (): Promise<boolean> => {
    try {
      // Get the session
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user?.id) {
        return false;
      }

      try {
        // First check the subscriptions table
        const { data: subscriptionData, error: subscriptionError } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', session.user.id)
          .order('created_at', { ascending: false })
          .limit(1)
          .maybeSingle();

        if (subscriptionError) {
          console.error('Error checking subscription:', subscriptionError);
          // Continue to check subscription_mappings even if there's an error here
        } else {
          // Check if user has an active subscription in the subscriptions table
          if (subscriptionData &&
              subscriptionData.status &&
              ['active', 'trialing'].includes(subscriptionData.status)) {
            return true;
          }
        }

        // If no active subscription found in subscriptions table, check subscription_mappings
        // Get user's email from session
        const userEmail = session.user.email;

        if (userEmail) {
          const { data: mappingData, error: mappingError } = await supabase
            .from('subscription_mappings')
            .select('*')
            .eq('email', userEmail)
            .maybeSingle();

          if (mappingError) {
            console.error('Error checking subscription_mappings:', mappingError);
          } else if (mappingData) {
            // If there's any entry for this email in subscription_mappings, consider them paid
            return true;
          }
        }

        // If we get here, the user does NOT have an active subscription in either table
        return false;
      } catch (dbError) {
        console.error('Database error when checking subscription:', dbError);
        // If there's a database error, assume no subscription to be safe
        return false;
      }
    } catch (error) {
      console.error('Error in checkUserSubscription:', error);
      // In case of error, assume no subscription to be safe
      return false;
    }
  };

  // Function to handle plan selection
  const handleSelectPlan = async (planType: string) => {
    try {

      // Double-check subscription status one more time before redirecting
      try {
        const hasActiveSubscription = await checkUserSubscription();

        if (hasActiveSubscription) {
          setSubscriptionCookie(true);
          setIsLoading(false);
          onClose();
          return;
        }

        // If we get here, the user does NOT have an active subscription
      } catch (subCheckError) {
        console.error('Error in final subscription check:', subCheckError);
        // Continue with redirect if there's an error checking subscription
      }

      // Check if there's a selected plan type from URL parameters
      const selectedPlanType = getSelectedPlanType();

      // Check for no-trial parameters
      const urlParams = new URLSearchParams(window.location.search);
      const basicNoTrial = urlParams.get('basicnotrial') !== null;
      const proNoTrial = urlParams.get('pronotrial') !== null;
      const skipTrial = basicNoTrial || proNoTrial;

      // Get the appropriate price ID based on the selected plan or default to basic
      const priceId = selectedPlanType ?
        getPriceIdForPlanType(selectedPlanType) :
        "price_1ROYLKDebmd1GpTvct491Kw6"; // Default to basic plan if no selection

      try {
        // Get the session
        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.access_token) {
          throw new Error("No authentication token found");
        }

        const functionUrl = new URL('/functions/v1/stripe-checkout', import.meta.env.VITE_SUPABASE_URL).toString();

        let response;
        try {
          response = await fetch(functionUrl, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${session.access_token}`
            },
            body: JSON.stringify({
              action: "create-checkout-session",
              priceId,
              returnUrl: window.location.origin + "?subscription_success=true",
              skipTrial: skipTrial
            }),
          });

          if (!response.ok) {
            console.error(`Stripe API error: ${response.status} ${response.statusText}`);
            throw new Error(`HTTP error! status: ${response.status}`);
          }
        } catch (fetchError) {
          console.error('Error fetching from Stripe API:', fetchError);
          // If we can't reach the Stripe API, redirect to Stripe directly
          // Instead of using hardcoded payment links, create a direct checkout session
          // This is more reliable for international users on all devices
          try {
            // Get the appropriate price ID based on the selected plan or default to basic
            const selectedPlanType = getSelectedPlanType();
            const priceId = selectedPlanType ?
              getPriceIdForPlanType(selectedPlanType) :
              "price_1ROYLKDebmd1GpTvct491Kw6"; // Default to basic plan

            // Get user email from Supabase
            const { data: { session: authSession } } = await supabase.auth.getSession();
            const userEmail = authSession?.user?.email || '';

            // Create a checkout session directly with Stripe
            const stripe = await loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);
            if (!stripe) throw new Error("Stripe failed to load");

            // Redirect to Stripe Checkout directly
            window.location.href = `https://checkout.stripe.com/c/pay/${priceId}?prefilled_email=${encodeURIComponent(userEmail)}&locale=auto`;
          } catch (stripeError) {
            console.error('Stripe direct checkout error:', stripeError);
            // Last resort fallback
            window.location.href = 'https://buy.stripe.com/aEU9DL9Xt0H9fO8cMN';
          }
          return;
        }

        let data;
        try {
          data = await response.json();
        } catch (e) {
          throw new Error(`Invalid JSON response`);
        }

        const { sessionId, error } = data;
        if (error) throw new Error(error);
        if (!sessionId) throw new Error("No session ID returned from server");

        const stripe = await loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);
        if (!stripe) throw new Error("Stripe failed to load");

        const { error: redirectError } = await stripe.redirectToCheckout({ sessionId });
        if (redirectError) throw new Error(`Stripe redirect failed: ${redirectError.message}`);

        // Clear the selected plan type after successful redirect
        clearSelectedPlanType();
      } catch (authError) {
        console.error('Authentication or Stripe API error:', authError);
        setIsLoading(false);
      }
    } catch (error) {
      // Show error and reset loading state
      console.error('Error redirecting to Stripe:', error);
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-start sm:items-center justify-center bg-black/60 backdrop-blur-md overflow-y-auto scroll-smooth" id="onboarding-modal">
      <div className="w-full min-h-full flex items-center justify-center py-6 px-3 sm:p-4">
        {isLoggedIn ? (
          // Loading Spinner
          <motion.div
            className="relative bg-[#121212]/60 rounded-2xl w-full max-w-[500px] overflow-hidden border border-white/[0.08] backdrop-blur-sm mx-3 my-6 sm:my-0 min-h-[200px] flex items-center justify-center"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{
              opacity: 1,
              scale: 1,
              boxShadow: "0 0 0 1px rgba(255, 255, 255, 0.08), 0 10px 30px rgba(0, 0, 0, 0.3)"
            }}
          >
            <div className="flex flex-col items-center gap-4">
              <Loader2 className="w-8 h-8 text-white animate-spin" />
              <p className="text-white/60 text-sm">Checking subscription status...</p>
              <p className="text-white/40 text-xs">Please wait while we verify your account</p>
            </div>
          </motion.div>
        ) : (
          // Google Auth Card
          <motion.div
            className="relative bg-[#121212] rounded-xl w-full max-w-md overflow-hidden border border-gray-800/30 mx-3 my-4 sm:my-0"
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{
              scale: 1,
              opacity: 1
            }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.3 }}
          >
            <div className="min-h-[400px] flex flex-col items-center py-8 px-8">
              {/* Logo/icon */}
              <div className="flex items-center justify-center mb-6">
                <img
                  src="http://thecodingkid.oyosite.com/logo_only.png"
                  alt="Logo"
                  className="w-14 h-14 rounded-xl"
                />
              </div>

              <h2 className="text-2xl font-medium text-white text-center mb-2">
                Sign in with Google
              </h2>

              <p className="text-white/70 text-base text-center mb-8">
                Use your Google account to start trading with confidence
              </p>

              <div className="w-full flex flex-col items-center space-y-6">
                {/* Terms checkbox */}
                <div className="flex items-start space-x-2">
                  <Checkbox
                    id="terms"
                    checked={termsAgreed}
                    onCheckedChange={(checked) => setTermsAgreed(checked as boolean)}
                    className="mt-0.5"
                    disabled={isSubmitting}
                  />
                  <label
                    htmlFor="terms"
                    className="text-sm text-white/70 leading-tight cursor-pointer"
                  >
                    I agree to the{" "}
                    <a href="/terms" className="text-[#9A9A9A] hover:underline">Terms</a>
                    {" "}and{" "}
                    <a href="/privacy" className="text-[#9A9A9A] hover:underline">Privacy Policy</a>
                  </label>
                </div>

                {loginError && (
                  <div className="text-red-500 text-sm mt-1 w-full">
                    {loginError}
                  </div>
                )}

                {/* Google Auth Button */}
                <button
                  type="button"
                  onClick={handleGoogleSignIn}
                  disabled={!termsAgreed || isSubmitting}
                  className="w-full mt-4 bg-[#1A1A1A] border border-[#222222] hover:bg-[#252525] text-white text-sm py-3 rounded-md font-medium transition-colors flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {/* Google icon */}
                  <svg viewBox="0 0 24 24" width="16" height="16" className="mr-1">
                    <g transform="matrix(1, 0, 0, 1, 27.009001, -39.238998)">
                      <path fill="#4285F4" d="M -3.264 51.509 C -3.264 50.719 -3.334 49.969 -3.454 49.239 L -14.754 49.239 L -14.754 53.749 L -8.284 53.749 C -8.574 55.229 -9.424 56.479 -10.684 57.329 L -10.684 60.329 L -6.824 60.329 C -4.564 58.239 -3.264 55.159 -3.264 51.509 Z"/>
                      <path fill="#34A853" d="M -14.754 63.239 C -11.514 63.239 -8.804 62.159 -6.824 60.329 L -10.684 57.329 C -11.764 58.049 -13.134 58.489 -14.754 58.489 C -17.884 58.489 -20.534 56.379 -21.484 53.529 L -25.464 53.529 L -25.464 56.619 C -23.494 60.539 -19.444 63.239 -14.754 63.239 Z"/>
                      <path fill="#FBBC05" d="M -21.484 53.529 C -21.734 52.809 -21.864 52.039 -21.864 51.239 C -21.864 50.439 -21.724 49.669 -21.484 48.949 L -21.484 45.859 L -25.464 45.859 C -26.284 47.479 -26.754 49.299 -26.754 51.239 C -26.754 53.179 -26.284 54.999 -25.464 56.619 L -21.484 53.529 Z"/>
                      <path fill="#EA4335" d="M -14.754 43.989 C -12.984 43.989 -11.404 44.599 -10.154 45.789 L -6.734 42.369 C -8.804 40.429 -11.514 39.239 -14.754 39.239 C -19.444 39.239 -23.494 41.939 -25.464 45.859 L -21.484 48.949 C -20.534 46.099 -17.884 43.989 -14.754 43.989 Z"/>
                    </g>
                  </svg>
                  <span className="text-sm">Continue with Google</span>
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}
