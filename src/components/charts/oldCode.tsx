import React, { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import { Card } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { AlertTriangle, TrendingUp, TrendingDown, BarChart3, LineChart, CandlestickChart, Info } from 'lucide-react';
import ReactECharts from 'echarts-for-react';
import { useQuery } from '@tanstack/react-query';
import { fetchPolygonChartData } from '@/services/polygonService';
import { Button } from '@/components/ui/button';
import { isCryptoCurrency, formatPolygonSymbol } from '@/utils/symbolUtils';
import domtoimage from 'dom-to-image';
import './AdvancedStockChart.css';

// Create a simple theme hook to replace the missing one
const useTheme = () => {
  // Default to dark theme, you can adjust this based on your app's needs
  return {
    theme: 'dark',
    setTheme: () => {},
  };
};

// Trading strategy visualization interfaces
interface TradingStrategy {
  name?: string;
  direction?: 'LONG' | 'SHORT';
  confidence?: 'LOW' | 'MEDIUM' | 'HIGH';
  entryPoints?: PricePoint[];
  exitPoints?: PricePoint[];
  stopLoss?: number;
  supportLevels?: number[];
  resistanceLevels?: number[];
  takeProfitTargets?: PricePoint[];
  riskRewardRatio?: string;
  buyZones?: PriceZone[];
  sellZones?: PriceZone[];
  recommendation?: string;
  currentPrice?: number;
}

interface PricePoint {
  price: number;
  timestamp: number;
  label?: string;
}

interface PriceZone {
  startPrice: number;
  endPrice: number;
  startTimestamp: number;
  endTimestamp: number;
  label?: string;
}

interface AdvancedStockChartProps {
  symbol: string;
  height?: number;
  showVolume?: boolean;
  showControls?: boolean;
  theme?: 'light' | 'dark' | 'auto';
  chartType?: ChartType;
  timeframe?: TimeframeOption;
  onSymbolChange?: (symbol: string) => void;
  yAxisDraggable?: boolean;
  defaultYAxisScale?: 'auto' | 'compressed';
  yAxisPosition?: 'left' | 'right';
  yAxisGap?: number;
  maxDataPoints?: number;
  price?: string;
  percentChange?: string;
  // Add trading strategy prop
  tradingStrategy?: TradingStrategy;
}

type ChartType = 'candle' | 'line' | 'area';
type TimeframeOption = '1D' | '5D' | '1M' | 'YTD' | '1Y';

export function AdvancedStockChart({
  symbol,
  height = 400,
  showVolume = true,
  showControls = true,
  theme = 'dark',
  chartType = 'line',
  timeframe: timeframeProp,
  onSymbolChange,
  yAxisDraggable,
  defaultYAxisScale,
  yAxisPosition,
  yAxisGap,
  maxDataPoints,
  price,
  percentChange,
  tradingStrategy
}: AdvancedStockChartProps) {
  const [chartTypeState, setChartType] = useState<ChartType>(chartType);
  const [containerHeight, setContainerHeight] = useState(height);
  const [timeframe, setTimeframe] = useState<TimeframeOption>(timeframeProp || '1M');
  const chartInstanceRef = useRef<any>(null);
  const chartContainerRef = useRef<HTMLDivElement>(null);

  // Change to useEffect to handle the async check
  const [isCrypto, setIsCrypto] = useState(false);

  // Replace the tooltipPosition state with a ref since we don't need to trigger re-renders
  const tooltipPositionRef = useRef({ x: 0, y: 0 });

  // Add state for custom tooltip
  const [tooltipData, setTooltipData] = useState<{
    visible: boolean;
    x: number;
    y: number;
    price: string;
    date: string;
    dataPoint?: any;
  }>({
    visible: false,
    x: 0,
    y: 0,
    price: '',
    date: '',
  });

  // Add state for crosshair
  const [crosshairPosition, setCrosshairPosition] = useState<{
    visible: boolean;
    x: number;
    y: number;
    xValue: string;
    yValue: string;
  }>({
    visible: false,
    x: 0,
    y: 0,
    xValue: '',
    yValue: ''
  });

  // Helper function to format numbers with commas - moved to the top of the component
  const formatNumberWithCommas = (num: number | string): string => {
    // Convert to number first
    const numValue = typeof num === 'string' ? parseFloat(num) : num;

    // For very small numbers (like crypto prices under $0.01)
    if (numValue < 0.01) {
      // Find the first non-zero digit after decimal and show a few more digits
      const priceStr = numValue.toString();
      const firstNonZero = priceStr.match(/\.0*[1-9]/)?.[0].length - 1 || 8;
      return numValue.toFixed(firstNonZero + 2); // Show 2 more digits after first non-zero
    }

    // For numbers >= 1000, add commas
    if (numValue >= 1000) {
      const parts = numValue.toFixed(2).split('.');
      parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      return parts.join('.');
    }

    // For numbers between 0.01 and 999.99, just show appropriate decimal places
    if (numValue >= 0.01) {
      // If it's a whole number, don't show decimals
      if (Number.isInteger(numValue)) {
        return numValue.toString();
      }
      // Otherwise show up to 2 decimal places
      return numValue.toFixed(2);
    }

    // Fallback
    return numValue.toString();
  };

  // Check if this is a one-letter symbol which might need special handling
  const isOneLetterSymbol = symbol?.length === 1;
  useEffect(() => {
    // One-letter symbol handling
  }, [symbol, isOneLetterSymbol]);

  // Update timeframe when prop changes
  useEffect(() => {
    if (timeframeProp) {
      setTimeframe(timeframeProp as TimeframeOption);
    }
  }, [timeframeProp]);

  // Use the prop value or the state value
  const effectiveChartType = showControls ? chartTypeState : chartType;

  // Fetch data from Polygon
  const { data: chartData, isLoading, isError } = useQuery({
    queryKey: ['polygonChartData', symbol, timeframe, isCrypto],
    queryFn: async () => {
      // Wait for the symbol to be formatted
      const formattedSymbol = await formatPolygonSymbol(symbol);
      const data = await fetchPolygonChartData({
        symbol: formattedSymbol,
        timeframe
      });
      return data;
    },
    enabled: !!symbol && isCrypto !== null,
    refetchInterval: timeframe === '1D' ? 60000 : false,
    retry: 2,
    staleTime: 5 * 60 * 1000,
  });

  useEffect(() => {
    // Chart data updated effect
  }, [chartData, symbol]);

  // Handle chart instance cleanup
  useEffect(() => {
    return () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.dispose();
        chartInstanceRef.current = null;
      }
    };
  }, []);

  // Handle window resize with debounce
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleResize = () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      timeoutId = setTimeout(() => {
        if (chartInstanceRef.current) {
          chartInstanceRef.current.resize();
        }
      }, 100);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, []);

  // Dynamic height adjustment
  useEffect(() => {
    const updateHeight = () => {
      if (chartContainerRef.current) {
        const height = chartContainerRef.current.clientHeight;
        setContainerHeight(height);
      }
    };

    updateHeight();
    window.addEventListener('resize', updateHeight);
    return () => window.removeEventListener('resize', updateHeight);
  }, []);

  // Change to useEffect to handle the async check
  useEffect(() => {
    let isMounted = true;

    const checkIfCrypto = async () => {
      if (!symbol) return;
      try {
        const result = await isCryptoCurrency(symbol);
        if (isMounted) {
          setIsCrypto(result);
        }
      } catch (error) {
        // Error handling
      }
    };

    checkIfCrypto();

    return () => {
      isMounted = false;
    };
  }, [symbol]);

  // Helper function to format dates for the strategy visualization - moved above createStrategyVisualizationSeries
  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    if (timeframe === '1D') {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (['1W', '1M'].includes(timeframe)) {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric', year: '2-digit' });
    }
  };

  // Function to create trading strategy visualization series
  const createStrategyVisualizationSeries = useCallback((strategy: TradingStrategy, chartPrices: any[]) => {
    if (!strategy || !chartData?.data?.length) return [];

    const series: any[] = [];

    // Check if trade is LONG or SHORT to apply appropriate colors
    const isLong = strategy.direction === 'LONG';

    // CORRECTED COLOR LOGIC - SL is ALWAYS red, TP is ALWAYS green
    const slColor = '#ff3a50'; // Stop Loss is always red
    const tpColor = '#00ff88'; // Take Profit is always green

    // Process stop loss with improved styling - ONLY if value exists and is valid
    const stopLoss = strategy.stopLoss;
    if (stopLoss && !isNaN(stopLoss) && stopLoss > 0) {
      series.push({
        name: 'Stop Loss',
        type: 'line',
        markLine: {
          symbol: 'none',
          lineStyle: {
            color: slColor,
            width: 2,
            type: 'dashed'
          },
          label: {
            show: true,
            position: 'start',
            formatter: `SL ${formatNumberWithCommas(stopLoss)}`,
            backgroundColor: 'rgba(34, 19, 21, 0.95)',
            color: slColor,
            padding: [3, 6],
            borderRadius: 4,
            fontSize: 11,
            fontWeight: 'bold'
          },
          data: [{
            name: 'Stop Loss',
            yAxis: stopLoss
          }],
          animation: false,
          animationDuration: 0
        },
        silent: true,
        z: 100
      });
    }

    // Process take profit targets - ONLY SHOW THE FIRST ONE with improved styling
    let tpPrice = null;
    if (strategy.takeProfitTargets && strategy.takeProfitTargets.length > 0) {
      // Only use the first TP target
      const target = strategy.takeProfitTargets[0];
      if (target && target.price && !isNaN(target.price) && target.price > 0) {
        tpPrice = target.price;

        series.push({
          name: 'Take Profit',
          type: 'line',
          markLine: {
            symbol: 'none',
            lineStyle: {
              color: tpColor,
              width: 2,
              type: 'solid'
            },
            label: {
              show: true,
              position: 'start',
              formatter: `TP ${formatNumberWithCommas(tpPrice)}`,
              backgroundColor: 'rgba(19, 34, 21, 0.95)',
              color: tpColor,
              padding: [3, 6],
              borderRadius: 4,
              fontSize: 11,
              fontWeight: 'bold'
            },
            data: [{
              name: 'Take Profit',
              yAxis: tpPrice
            }],
            animation: false,
            animationDuration: 0
          },
          silent: true,
          z: 100
        });
      }
    }

    // Add a SIMPLIFIED visualization between TP and SL without gradients
    if (stopLoss && tpPrice) {
      // Only add zone if TP and SL are correctly positioned based on trade direction
      // For LONG trades: TP should be higher than SL
      // For SHORT trades: TP should be lower than SL
      const isValidSetup = (isLong && tpPrice > stopLoss) || (!isLong && tpPrice < stopLoss);

      // Always add lines regardless of setup validity
      const startY = Math.min(tpPrice, stopLoss);
      const endY = Math.max(tpPrice, stopLoss);

      // Add zone only for valid setups - with simplified styling (no gradients)
      if (isValidSetup && chartData.data.length > 0) {
        // Get the timestamps from the chart data
        const timestamps = chartData.data.map((item: any) => item.timestamp);
        const xMin = Math.min(...timestamps);
        const xMax = Math.max(...timestamps);

        // Just add a solid area between the two lines with flat color
        series.push({
          type: 'custom',
          renderItem: function(params, api) {
            const yMin = api.coord([0, startY])[1];
            const yMax = api.coord([0, endY])[1];
            const xCoords = api.coord([0, 0]);
            const width = params.coordSys.width;

            const rectShape = {
              x: xCoords[0],
              y: yMax,
              width: width,
              height: yMin - yMax
            };

            return {
              type: 'rect',
              shape: rectShape,
              style: {
                fill: isLong ? 'rgba(0, 255, 136, 0.04)' : 'rgba(255, 58, 80, 0.04)'
              },
              zlevel: 79
            };
          },
          data: [[0, startY]],
          tooltip: {
            show: false
          },
          emphasis: {
            disabled: true
          },
          silent: true,
          z: 79
        });
      }
    }

    return series;
  }, [chartData?.data, formatNumberWithCommas]);

  // Memoize the formatted data to prevent recalculation on every render
  const formattedData = useMemo(() => {
    if (!chartData?.data?.length) return [];
    return chartData.data.map((item: any) => ({
      timestamp: Number(item.timestamp),
      open: Number(item.open),
      close: Number(item.close),
      high: Number(item.high),
      low: Number(item.low),
      volume: Number(item.volume)
    }));
  }, [chartData?.data]);

  // Calculate data ranges for better visualization - also memoized
  const { minPrice, maxPrice, priceRange } = useMemo(() => {
    if (!formattedData.length) return { minPrice: 0, maxPrice: 0, priceRange: 0 };
    const prices = formattedData.map((item: any) => [
      item.low,
      item.high
    ]).flat();

    return {
      minPrice: Math.min(...prices),
      maxPrice: Math.max(...prices),
      priceRange: Math.max(...prices) - Math.min(...prices)
    };
  }, [formattedData]);

  const getChartOptions = useCallback(() => {
    if (!chartData?.data?.length || !formattedData.length) {
      return null;
    }

    // Enhanced theme colors with bright green and dark red
    const colors = {
      up: '#00ff88',     // Bright green
      down: '#ff3632',   // Vibrant red
      line: '#8b5cf6',   // Purple line
      area: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [{
          offset: 0,
          color: 'rgba(139, 92, 246, 0.3)' // More visible purple
        }, {
          offset: 1,
          color: 'rgba(139, 92, 246, 0.02)' // Nearly transparent
        }]
      },
      background: '#0A0A0C',
      grid: 'rgba(255, 255, 255, 0.03)',
      text: 'rgba(255, 255, 255, 0.5)',
      axis: 'rgba(255, 255, 255, 0.08)'
    };

    // Adjust y-axis range with tighter margins for better price visibility
    const yAxisMargin = priceRange * 0.05; // 5% margin
    const yAxisMin = minPrice - yAxisMargin;
    const yAxisMax = maxPrice + yAxisMargin;

    // Base options
    const options: any = {
      animation: effectiveChartType === 'candle',
      animationDuration: effectiveChartType === 'candle' ? 800 : 0,
      animationThreshold: 5000,
      progressive: 0,
      progressiveThreshold: 0,
      blendMode: 'source-over',
      renderer: 'canvas',
      hoverLayerThreshold: Infinity,
      useUTC: true,
      grid: [{
        left: '0%',
        right: '-7%',
        top: '5%',
        bottom: '0%',
        containLabel: true
      }],
      tooltip: {
        show: false
      },
      axisPointer: {
        show: false,
        status: 'hide',
        triggerOn: 'none',
        type: 'none'
      },
      xAxis: {
        type: 'category',
        data: formattedData.map(item => formatDate(item.timestamp)),
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: {
          show: window.innerWidth > 640,
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 11,
          fontWeight: 500,
          margin: 4,
          interval: timeframe === '1D' ? 'auto' : Math.floor(formattedData.length / 6),
          align: 'center',
          position: 'middle'
        },
        splitLine: { show: false },
        axisPointer: {
          show: false,
          status: 'hide'
        }
      },
      yAxis: {
        type: 'value',
        position: yAxisPosition || 'right',
        min: yAxisMin,
        max: yAxisMax,
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: { show: false },
        axisLabel: {
          show: window.innerWidth > 640,
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 11,
          fontWeight: 500,
          margin: 3,
          padding: [0, 0, 0, 0],
          formatter: (value: number) => {
            return `$${formatNumberWithCommas(value)}`;
          },
          align: 'right',
          verticalAlign: 'middle',
          inside: false
        },
        axisPointer: {
          show: false,
          status: 'hide'
        },
        scale: true,
        splitNumber: 6,
        z: 100
      },
      series: effectiveChartType === 'candle' ? [{
        name: symbol,
        type: 'candlestick',
        data: formattedData.map(item => [
          item.open,
          item.close,
          item.low,
          item.high
        ]),
        animation: false,
        animationDuration: 0,
        itemStyle: {
          color: colors.up,
          color0: colors.down,
          borderColor: colors.up,
          borderColor0: colors.down,
          borderWidth: 1,
          opacity: 1
        }
      }] : [
        // Area series
        {
          name: `${symbol}-area`,
          type: 'line',
          data: formattedData.map(item => item.close),
          smooth: false,
          symbol: 'none',
          animation: false,
          z: 9,
          lineStyle: {
            width: 0
          },
          areaStyle: {
            color: colors.area,
            opacity: 1,
            origin: 'start'
          }
        },
        // Line series
        {
          name: `${symbol}-line`,
          type: 'line',
          data: formattedData.map(item => item.close),
          smooth: false,
          symbol: 'none',
          animation: false,
          z: 10,
          lineStyle: {
            color: colors.line,
            width: 2
          }
        }
      ]
    };

    // Volume series
    if (showVolume) {
      options.series.push({
        name: 'Volume',
        type: 'bar',
        xAxisIndex: 0,
        yAxisIndex: 0,
        data: formattedData.map(item => ({
          value: item.volume,
          itemStyle: {
            color: item.close >= item.open ? colors.up : colors.down,
            opacity: 0.3
          }
        })),
        animation: effectiveChartType === 'candle',
        animationDuration: effectiveChartType === 'candle' ? 800 : 0,
        z: 5
      });
    }

    // Strategy lines
    if (tradingStrategy) {
      const strategySeries = createStrategyVisualizationSeries(tradingStrategy, formattedData.map((item: any) => [item.low, item.high]));
      strategySeries.forEach(series => {
        series.animation = effectiveChartType === 'candle';
        series.animationDuration = effectiveChartType === 'candle' ? 800 : 0;
      });
      options.series = [...options.series, ...strategySeries];
    }

    return options;
  }, [chartData?.data, formattedData, minPrice, maxPrice, priceRange, effectiveChartType, timeframe, showVolume, symbol, tradingStrategy, createStrategyVisualizationSeries, formatNumberWithCommas]);

  const chartOptions = useMemo(() => {
    if (!chartData?.data?.length) return null;
    return getChartOptions();
  }, [chartData?.data, getChartOptions]);

  // Calculate price change
  const priceChange = useMemo(() => {
    if (!chartData?.data?.length) return null;
    const firstPrice = chartData.data[0].open;
    const lastPrice = chartData.data[chartData.data.length - 1].close;
    const change = lastPrice - firstPrice;
    const percentChange = (change / firstPrice) * 100;
    return {
      change,
      percentChange,
      isPositive: change >= 0
    };
  }, [chartData]);

  // Update the title to show if it's a cryptocurrency
  const chartTitle = useMemo(() => {
    if (!symbol) return '';
    return isCrypto ? `${symbol.toUpperCase()}/USD` : symbol.toUpperCase();
  }, [symbol, isCrypto]);

  // Update the chart instance ref when ready
  const handleChartReady = (instance: any) => {
    if (!chartData?.data) return;
    chartInstanceRef.current = instance;
    chartInstanceRef.current.startTime = Date.now();

    const zr = instance.getZr();
    const chartContainer = document.getElementById('chart-container');
    const chartRect = chartContainer?.getBoundingClientRect();

    zr.on('mousemove', (params: any) => {
      if (!chartRect) return;

      const pointInGrid = instance.convertFromPixel({ seriesIndex: 0 }, [params.offsetX, params.offsetY]);
      if (pointInGrid) {
        const dataIndex = Math.min(Math.max(0, Math.round(pointInGrid[0])), formattedData.length - 1);
        if (dataIndex >= 0 && dataIndex < formattedData.length) {
          const item = formattedData[dataIndex];
          const priceInfo = effectiveChartType === 'candle'
            ? `O: $${formatNumberWithCommas(item.open)}
H: $${formatNumberWithCommas(item.high)}
L: $${formatNumberWithCommas(item.low)}
C: $${formatNumberWithCommas(item.close)}`
            : `$${formatNumberWithCommas(item.close)}`;

          // Get the actual coordinates for the data point
          const coords = instance.convertToPixel({ seriesIndex: 0 }, [dataIndex, item.close]);

          setTooltipData({
            visible: true,
            x: coords[0],
            y: coords[1],
            price: priceInfo,
            date: formatDate(item.timestamp),
            dataPoint: item
          });

          setCrosshairPosition({
            visible: true,
            x: coords[0],
            y: coords[1],
            xValue: formatDate(item.timestamp),
            yValue: `$${formatNumberWithCommas(item.close)}`
          });
        }
      }
    });

    zr.on('globalout', () => {
      setTooltipData(prev => ({ ...prev, visible: false }));
      setCrosshairPosition(prev => ({ ...prev, visible: false }));
    });
  };

  // Function to handle the export
  const handleExportChart = () => {
    const chartElement = document.getElementById('chart-container'); // Ensure this ID matches your chart container
    const buttonElement = document.querySelector('.absolute.left-3.bottom-\\[4px\\]') as HTMLElement; // Select the button and cast to HTMLElement

    if (buttonElement) {
        buttonElement.style.display = 'none'; // Hide the button
    }

    if (chartElement) {
        const ticker = symbol.toUpperCase(); // Get the ticker
        const date = new Date().toISOString().split('T')[0]; // Get the current date in YYYY-MM-DD format
        const filename = `${ticker}_${date}.png`; // Set the filename

        domtoimage.toPng(chartElement)
            .then((dataUrl) => {
                const link = document.createElement('a');
                link.href = dataUrl;
                link.download = filename; // Use the new filename
                link.click();
            })
            .catch((error) => {
                // Error handling
            })
            .finally(() => {
                if (buttonElement) {
                    buttonElement.style.display = 'block'; // Show the button again
                }
            });
    }
  };

  if (isError) {
    return (
      <Card className="w-full bg-[#161A23] border-[#1E2330] p-6 rounded-xl">
        <div className="flex items-center justify-center gap-2 text-red-400">
          <AlertTriangle className="w-5 h-5" />
          <p className="text-sm font-medium">Failed to load market data</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="w-full h-full bg-[#0A0A0C] border-[#1A1A1C] rounded-xl shadow-lg overflow-hidden">
      {/* Price Display */}
      <div className="flex items-center justify-between p-1.5 sm:p-2.5 border-b border-[#1A1A1C] bg-[#0A0A0C]">
        <div className={`flex items-center gap-1.5 transition-opacity duration-200 ${isLoading ? 'opacity-0' : 'opacity-100'} whitespace-nowrap`}>
          <span className="text-lg font-bold text-white/90">${formatNumberWithCommas(price || '0.00')}</span>
          <div className={`px-2 py-0.5 ml-1.5 rounded-sm h-5 flex items-center justify-center ${
            (percentChange || '').startsWith('+')
              ? 'bg-[#132215] text-[#00ff88]'
              : 'bg-[#221315] text-[#ff3a50]'
          }`}>
            <span className="text-xs font-medium">{percentChange || '0.00%'}</span>
          </div>
        </div>

        <div className="flex items-center gap-1">
          <div className="flex items-center">
            {['1D', '5D', '1M', 'YTD', '1Y'].map((period) => (
              <Button
                key={period}
                size="sm"
                variant={timeframe === period ? "default" : "ghost"}
                className={`h-6 sm:h-7 rounded-full px-2 sm:px-3 py-0 text-xs font-medium ${
                  timeframe === period
                    ? "bg-[#2A2A2C] text-white/90 border border-[#3A3A3C]"
                    : "bg-transparent text-white/60 hover:bg-[#1A1A1C]/40"
                } ${
                  ['5D', 'YTD'].includes(period) ? 'hidden sm:inline-flex' : ''
                }`}
                onClick={() => setTimeframe(period as TimeframeOption)}
              >
                {period}
              </Button>
            ))}
          </div>

          <div className="flex items-center pl-1 sm:pl-2 border-l border-[#1A1A1C]">
            <Button
              size="sm"
              variant={effectiveChartType === 'line' ? "default" : "ghost"}
              className={`h-6 sm:h-7 rounded-full px-1.5 sm:px-2 py-0 text-xs font-medium ${
                effectiveChartType === 'line'
                  ? "bg-[#2A2A2C] text-white/90 border border-[#3A3A3C]"
                  : "bg-transparent text-white/60 hover:bg-[#1A1A1C]/40"
              }`}
              onClick={() => setChartType('line')}
            >
              <LineChart className="h-3 w-3 sm:h-3.5 sm:w-3.5" />
            </Button>
            <Button
              size="sm"
              variant={effectiveChartType === 'candle' ? "default" : "ghost"}
              className={`h-6 sm:h-7 rounded-full px-1.5 sm:px-2 py-0 text-xs font-medium ${
                effectiveChartType === 'candle'
                  ? "bg-[#2A2A2C] text-white/90 border border-[#3A3A3C]"
                  : "bg-transparent text-white/60 hover:bg-[#1A1A1C]/40"
              }`}
              onClick={() => setChartType('candle')}
            >
              <CandlestickChart className="h-3 w-3 sm:h-3.5 sm:w-3.5" />
            </Button>
          </div>
        </div>
      </div>

      <div ref={chartContainerRef} id="chart-container" className="w-full h-full relative">
        {isLoading ? (
          <div className="absolute inset-0 flex items-center justify-center bg-[#0A0A0C]">
            <div className="flex flex-col items-center">
              <div className="h-8 w-8 border-2 border-[#1A1A1C] border-t-[#8b5cf6] rounded-full animate-spin mb-3" style={{ animationDuration: '0.6s' }}></div>
              <div className="text-[#8b5cf6]/80 text-sm font-medium">Loading chart...</div>
            </div>
          </div>
        ) : chartData && chartOptions ? (
          <div className="w-full h-full p-0 opacity-0 animate-[fadeIn_0.3s_linear_forwards]" style={{ animationDelay: '0.1s' }}>
            <ReactECharts
              option={{
                ...chartOptions,
                backgroundColor: chartOptions?.backgroundColor || '#0A0A0C',
                useUTC: true,
                hoverLayerThreshold: 3000,
                tooltip: {
                  show: false,
                  trigger: 'none'
                },
                axisPointer: {
                  show: false,
                  status: 'hide'
                }
              }}
              style={{ height: '100%', width: '100%' }}
              theme="dark"
              notMerge={true}
              lazyUpdate={false}
              onChartReady={handleChartReady}
              opts={{
                renderer: 'canvas',
                devicePixelRatio: window.devicePixelRatio
              }}
            />
          </div>
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-[#0A0A0C]">
            <div className="flex flex-col items-center text-white/40">
              <AlertTriangle className="h-5 w-5 mb-2 text-white/30" />
              <span className="text-xs font-medium">No chart data available</span>
            </div>
          </div>
        )}

        {/* Custom Crosshair */}
        {crosshairPosition.visible && (
          <>
            {/* Vertical line */}
            <div
              className="absolute top-0 bottom-0 w-[1px] bg-white/10 pointer-events-none z-30"
              style={{ left: crosshairPosition.x }}
            />
            {/* Horizontal line */}
            <div
              className="absolute left-0 right-0 h-[1px] bg-white/10 pointer-events-none z-30"
              style={{ top: crosshairPosition.y }}
            />

            {/* Tracking Dot */}
            <div
              className="absolute pointer-events-none z-40"
              style={{
                left: crosshairPosition.x - 6,
                top: crosshairPosition.y - 6,
                transform: 'translate(0, 0)'
              }}
            >
              <div
                className={`w-3 h-3 rounded-full border-2 border-white shadow-lg ${
                  tooltipData.dataPoint && parseFloat(tooltipData.dataPoint.close) >= parseFloat(tooltipData.dataPoint.open)
                    ? 'bg-emerald-400 shadow-emerald-400/60'
                    : 'bg-red-400 shadow-red-400/60'
                }`}
                style={{
                  boxShadow: `0 0 12px ${
                    tooltipData.dataPoint && parseFloat(tooltipData.dataPoint.close) >= parseFloat(tooltipData.dataPoint.open)
                      ? 'rgba(52, 211, 153, 0.8)'
                      : 'rgba(248, 113, 113, 0.8)'
                  }`
                }}
              />
            </div>

            {/* X-axis label */}
            <div
              className="absolute bottom-0 px-2 py-1 text-[11px] font-medium bg-[#1a1a1c] text-white/80 rounded-sm pointer-events-none z-30"
              style={{
                left: crosshairPosition.x,
                transform: 'translateX(-50%)'
              }}
            >
              {crosshairPosition.xValue}
            </div>
          </>
        )}

        {/* Custom Tooltip */}
        {tooltipData.visible && (
          <div
            className="fixed z-50 bg-[#0a0a0b]/98 backdrop-blur-md px-3 py-2 rounded-lg border border-[#1a1a1c] shadow-2xl pointer-events-none"
            style={{
              left: tooltipData.x + 10,
              top: tooltipData.y,
              transform: 'translate(0, -50%)',
              maxWidth: '140px'
            }}
          >
            <div className="text-[11px] font-medium text-white/70 mb-1">
              {tooltipData.date}
            </div>
            <div className="text-xs font-bold text-white whitespace-pre leading-tight">
              {tooltipData.price}
            </div>
          </div>
        )}
      </div>
    </Card>
  );
}