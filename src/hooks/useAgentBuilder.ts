import { useState, useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Agent, AgentBlock } from '@/services/agentService';

// Block type enum
export enum BlockType {
  WHEN_RUN = 'WHEN_RUN', // Changed to uppercase for edge function compatibility
  INDICATOR = 'INDICATOR',
  PRICE = 'PRICE',
  FUNDAMENTAL = 'FUNDAMENTAL',
  CONDITION = 'CONDITION',
  TRIGGER = 'TRIGGER',
  OPERATOR = 'OPERATOR',
  BULLISH_CONFIDENCE_BOOST = 'BULLISH_CONFIDENCE_BOOST',
  BEARISH_CONFIDENCE_BOOST = 'BEARISH_CONFIDENCE_BOOST',
  CANDLE_PATTERN = 'CANDLE_PATTERN',
  // Additional block types
  CHART_PATTERN = 'CHART_PATTERN',
  BREAKOUT_DETECTION = 'BREAKOUT_DETECTION',
  GAP_ANALYSIS = 'GAP_ANALYSIS',
  CONSOLE_LOG = 'CONSOLE_LOG',
  MOMENTUM_INDICATOR = 'MOMENTUM_INDICATOR',
  MOVING_AVERAGE = 'MOVING_AVERAGE',
  POSITION_SIZE = 'POSITION_SIZE',
  SIGNAL = 'SIGNAL',
  // Technical Analysis blocks
  TREND_INDICATOR = 'TREND_INDICATOR',
  VOLUME_INDICATOR = 'VOLUME_INDICATOR',
  VOLATILITY_INDICATOR = 'VOLATILITY_INDICATOR',
  SUPPORT_RESISTANCE = 'SUPPORT_RESISTANCE',
  // Risk Management blocks
  STOP_LOSS = 'STOP_LOSS',
  TAKE_PROFIT = 'TAKE_PROFIT',
  RISK_LIMIT = 'RISK_LIMIT',
  // Time and Market blocks
  TIME_FILTER = 'TIME_FILTER',
  MARKET_BREADTH = 'MARKET_BREADTH',
  MULTI_TIMEFRAME = 'MULTI_TIMEFRAME',
  // Strategy blocks
  CONFLUENCE = 'CONFLUENCE',
  SCALE_STRATEGY = 'SCALE_STRATEGY',
  PARTIAL_PROFIT = 'PARTIAL_PROFIT',
  // Logic blocks
  AND = 'AND',
  OR = 'OR'
}

// Connection type enum
export enum ConnectionType {
  INPUT = 'input',
  OUTPUT = 'output',
  TRUE = 'true',
  FALSE = 'false'
}

// Connection interface
export interface Connection {
  sourceId: string;
  targetId: string;
  sourceHandle: string;
  targetHandle: string;
}

// Hook for managing agent builder state
// Ensure position is valid
const ensureValidPosition = (position: { x: number; y: number } | undefined, index = 0): { x: number; y: number } => {
  if (!position) {
    return { x: 100 + (index * 50), y: 100 + (index * 50) };
  }

  return {
    x: typeof position.x === 'number' && !isNaN(position.x) ? position.x : 100 + (index * 50),
    y: typeof position.y === 'number' && !isNaN(position.y) ? position.y : 100 + (index * 50)
  };
};

// Create a default "When Run" block
const createDefaultWhenRunBlock = (): AgentBlock => {
  const id = uuidv4();
  return {
    id,
    type: BlockType.WHEN_RUN,
    position: { x: 100, y: 100 },
    outputConnections: []
  };
};

export function useAgentBuilder(initialAgent?: Agent) {
  // Create a default "When Run" block if no initial agent is provided
  const defaultBlocks = initialAgent?.configuration?.blocks || [createDefaultWhenRunBlock()];
  const defaultEntryBlockId = initialAgent?.configuration?.entryBlockId ||
    (defaultBlocks.length > 0 ? defaultBlocks[0].id : '');

  // State for blocks and connections
  const [blocks, setBlocks] = useState<AgentBlock[]>(defaultBlocks);
  const [connections, setConnections] = useState<Connection[]>([]);
  const [entryBlockId, setEntryBlockId] = useState<string>(defaultEntryBlockId);
  const [agentName, setAgentName] = useState<string>(
    initialAgent?.name || 'New Agent'
  );
  const [agentDescription, setAgentDescription] = useState<string>(
    initialAgent?.description || ''
  );

  // Track changes for autosave
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const resetChanges = useCallback(() => {
    setHasChanges(false);
  }, []);

  // Undo/Redo state management
  interface AgentState {
    blocks: AgentBlock[];
    connections: Connection[];
    entryBlockId: string;
  }

  const [history, setHistory] = useState<AgentState[]>([]);
  const [historyIndex, setHistoryIndex] = useState<number>(-1);
  const [isUndoRedoOperation, setIsUndoRedoOperation] = useState<boolean>(false);

  // Save current state to history
  const saveToHistory = useCallback(() => {
    if (isUndoRedoOperation) return; // Don't save during undo/redo operations

    const currentState: AgentState = {
      blocks: [...blocks],
      connections: [...connections],
      entryBlockId
    };

    setHistory(prevHistory => {
      // Remove any future history if we're not at the end
      const newHistory = prevHistory.slice(0, historyIndex + 1);
      // Add current state
      newHistory.push(currentState);
      // Limit history to 50 states
      if (newHistory.length > 50) {
        newHistory.shift();
        return newHistory;
      }
      return newHistory;
    });

    setHistoryIndex(prevIndex => {
      const newIndex = Math.min(prevIndex + 1, 49);
      return newIndex;
    });
  }, [blocks, connections, entryBlockId, historyIndex, isUndoRedoOperation]);

  // Undo function
  const undo = useCallback(() => {
    if (historyIndex > 0) {
      setIsUndoRedoOperation(true);
      const previousState = history[historyIndex - 1];
      setBlocks(previousState.blocks);
      setConnections(previousState.connections);
      setEntryBlockId(previousState.entryBlockId);
      setHistoryIndex(historyIndex - 1);
      setHasChanges(true);

      // Reset the flag after state updates
      setTimeout(() => setIsUndoRedoOperation(false), 0);
    }
  }, [history, historyIndex]);

  // Redo function
  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      setIsUndoRedoOperation(true);
      const nextState = history[historyIndex + 1];
      setBlocks(nextState.blocks);
      setConnections(nextState.connections);
      setEntryBlockId(nextState.entryBlockId);
      setHistoryIndex(historyIndex + 1);
      setHasChanges(true);

      // Reset the flag after state updates
      setTimeout(() => setIsUndoRedoOperation(false), 0);
    }
  }, [history, historyIndex]);

  // Check if undo/redo is available
  const canUndo = historyIndex > 0;
  const canRedo = historyIndex < history.length - 1;

  // Add a new block
  const addBlock = useCallback((type: BlockType | string, position: { x: number; y: number }, properties: Record<string, any> = {}) => {
    // Save current state to history before making changes
    saveToHistory();

    const blockType = typeof type === 'string' ? type as BlockType : type;

    // Ensure position is valid
    const validPosition = ensureValidPosition(position, blocks.length);

    const newBlock: AgentBlock = {
      id: uuidv4(),
      type: blockType,
      position: validPosition,
      ...properties
    };

    // Set default properties based on block type
    switch (blockType) {
      case BlockType.INDICATOR:
        newBlock.indicator = properties.indicator || 'rsi';
        newBlock.parameters = properties.parameters || {};
        newBlock.outputConnections = properties.outputConnections || [];
        break;
      case BlockType.PRICE:
        newBlock.dataPoint = properties.dataPoint || 'close';
        newBlock.timeframe = properties.timeframe || undefined;
        newBlock.lookback = properties.lookback || 0;
        newBlock.outputConnections = properties.outputConnections || [];
        break;
      case BlockType.FUNDAMENTAL:
        newBlock.metric = properties.metric || 'peRatio';
        newBlock.parameters = properties.parameters || {};
        newBlock.outputConnections = properties.outputConnections || [];
        break;
      case BlockType.CONDITION:
        newBlock.operator = properties.operator || '>';
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.trueConnection = properties.trueConnection || undefined;
        newBlock.falseConnection = properties.falseConnection || undefined;
        break;
      case BlockType.TRIGGER:
        newBlock.signal = properties.signal || 'bullish';
        newBlock.confidence = properties.confidence || 75;
        newBlock.inputConnections = properties.inputConnections || [];
        break;
      case BlockType.OPERATOR:
        newBlock.operation = properties.operation || 'add';
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.outputConnections = properties.outputConnections || [];
        break;
      case BlockType.BULLISH_CONFIDENCE_BOOST:
        newBlock.percentage = properties.percentage || 10;
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.outputConnections = properties.outputConnections || [];
        break;
      case BlockType.BEARISH_CONFIDENCE_BOOST:
        newBlock.percentage = properties.percentage || 10;
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.outputConnections = properties.outputConnections || [];
        break;
      case BlockType.CANDLE_PATTERN:
        newBlock.pattern = properties.pattern || 'doji';
        newBlock.timeframe = properties.timeframe || 'day';
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.trueConnection = properties.trueConnection || undefined;
        newBlock.falseConnection = properties.falseConnection || undefined;
        newBlock.bullishConnection = properties.bullishConnection || undefined;
        newBlock.bearishConnection = properties.bearishConnection || undefined;
        newBlock.neutralConnection = properties.neutralConnection || undefined;
        break;
      case BlockType.CHART_PATTERN:
        newBlock.pattern = properties.pattern || 'any';
        newBlock.timeframe = properties.timeframe || 'day';
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.trueConnection = properties.trueConnection || undefined;
        newBlock.falseConnection = properties.falseConnection || undefined;
        newBlock.bullishConnection = properties.bullishConnection || undefined;
        newBlock.bearishConnection = properties.bearishConnection || undefined;
        newBlock.neutralConnection = properties.neutralConnection || undefined;
        break;
      case BlockType.BREAKOUT_DETECTION:
        newBlock.breakoutType = properties.breakoutType || 'support_resistance';
        newBlock.timeframe = properties.timeframe || 'day';
        newBlock.volumeConfirmation = properties.volumeConfirmation || false;
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.trueConnection = properties.trueConnection || undefined;
        newBlock.falseConnection = properties.falseConnection || undefined;
        break;
      case BlockType.GAP_ANALYSIS:
        newBlock.gapType = properties.gapType || 'any';
        newBlock.minGapSize = properties.minGapSize || 1;
        newBlock.timeframe = properties.timeframe || 'day';
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.gapUpConnection = properties.gapUpConnection || undefined;
        newBlock.gapDownConnection = properties.gapDownConnection || undefined;
        newBlock.noGapConnection = properties.noGapConnection || undefined;
        break;
      case BlockType.CONSOLE_LOG:
        newBlock.message = properties.message || 'Debug message';
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.outputConnections = properties.outputConnections || [];
        break;
      case BlockType.MOMENTUM_INDICATOR:
        newBlock.indicator = properties.indicator || 'rsi';
        newBlock.period = properties.period || 14;
        newBlock.timeframe = properties.timeframe || 'day';
        newBlock.overbought = properties.overbought || 70;
        newBlock.oversold = properties.oversold || 30;
        newBlock.outputConnections = properties.outputConnections || [];
        break;
      case BlockType.MOVING_AVERAGE:
        newBlock.averageType = properties.averageType || 'sma';
        newBlock.period = properties.period || 20;
        newBlock.timeframe = properties.timeframe || 'day';
        newBlock.source = properties.source || 'close';
        newBlock.outputConnections = properties.outputConnections || [];
        break;
    }

    setBlocks(prevBlocks => [...prevBlocks, newBlock]);
    setHasChanges(true);

    // If this is the first block, set it as the entry block
    if (blocks.length === 0) {
      setEntryBlockId(newBlock.id);
    }

    return newBlock.id;
  }, [blocks, saveToHistory]);

  // Update a block
  const updateBlock = useCallback((id: string, properties: Partial<AgentBlock>) => {
    setBlocks(prevBlocks => {
      // Find the block to update
      const blockToUpdate = prevBlocks.find(block => block.id === id);
      if (!blockToUpdate) return prevBlocks;

      // Special handling for position updates to ensure they're valid
      if (properties.position) {
        const position = properties.position;

        // Ensure position has valid x and y coordinates
        if (typeof position.x !== 'number' || typeof position.y !== 'number' ||
            isNaN(position.x) || isNaN(position.y)) {
          // If position is invalid, keep the existing position
          const { position: _, ...otherProperties } = properties;
          return prevBlocks.map(block =>
            block.id === id ? { ...block, ...otherProperties } : block
          );
        }
      }

      // Normal update with all properties
      return prevBlocks.map(block =>
        block.id === id ? { ...block, ...properties } : block
      );
    });

    setHasChanges(true);
  }, []);

  // Remove a block
  const removeBlock = useCallback((id: string) => {
    // Check if this is a "When Run" block
    const blockToRemove = blocks.find(block => block.id === id);
    if (blockToRemove?.type === BlockType.WHEN_RUN) {
      // Don't allow removing the "When Run" block
      return;
    }

    // Save current state to history before making changes
    saveToHistory();

    // Remove the block and clean up any references to it in other blocks
    setBlocks(prevBlocks => {
      const filteredBlocks = prevBlocks.filter(block => block.id !== id);

      // Clean up any references to the removed block in other blocks
      return filteredBlocks.map(block => {
        const cleanedBlock = { ...block };

        // Clean up inputConnections
        if (cleanedBlock.inputConnections) {
          cleanedBlock.inputConnections = cleanedBlock.inputConnections.filter(connId => connId !== id);
        }

        // Clean up outputConnections
        if (cleanedBlock.outputConnections) {
          cleanedBlock.outputConnections = cleanedBlock.outputConnections.filter(connId => connId !== id);
        }

        // Clean up trueConnection
        if (cleanedBlock.trueConnection === id) {
          cleanedBlock.trueConnection = undefined;
        }

        // Clean up falseConnection
        if (cleanedBlock.falseConnection === id) {
          cleanedBlock.falseConnection = undefined;
        }

        return cleanedBlock;
      });
    });

    // Remove connections involving the block
    setConnections(prevConnections =>
      prevConnections.filter(conn =>
        conn.sourceId !== id && conn.targetId !== id
      )
    );

    // If the entry block is removed, set a new entry block if possible
    if (id === entryBlockId && blocks.length > 1) {
      // Find the "When Run" block or use the first block
      const whenRunBlock = blocks.find(block => block.type === BlockType.WHEN_RUN);
      const remainingBlocks = blocks.filter(block => block.id !== id);
      setEntryBlockId(whenRunBlock?.id || remainingBlocks[0].id);
    } else if (blocks.length <= 1) {
      // This shouldn't happen since we don't allow removing the "When Run" block
      const whenRunBlock = createDefaultWhenRunBlock();
      setBlocks([whenRunBlock]);
      setEntryBlockId(whenRunBlock.id);
    }

    setHasChanges(true);
  }, [blocks, entryBlockId, saveToHistory]);

  // Add a connection between blocks
  const addConnection = useCallback((connection: Connection) => {
    // Save current state to history before making changes
    saveToHistory();
    // Check if connection already exists
    const connectionExists = connections.some(
      conn =>
        conn.sourceId === connection.sourceId &&
        conn.targetId === connection.targetId &&
        conn.sourceHandle === connection.sourceHandle &&
        conn.targetHandle === connection.targetHandle
    );

    if (connectionExists) {
      return false;
    }

    // Add the connection
    setConnections(prevConnections => [...prevConnections, connection]);

    // Update the blocks to reflect the connection
    setBlocks(prevBlocks => {
      return prevBlocks.map(block => {
        // Update source block
        if (block.id === connection.sourceId) {
          if (connection.sourceHandle === 'output') {
            return {
              ...block,
              outputConnections: [...(block.outputConnections || []), connection.targetId]
            };
          } else if (connection.sourceHandle === 'true') {
            return {
              ...block,
              trueConnection: connection.targetId
            };
          } else if (connection.sourceHandle === 'false') {
            return {
              ...block,
              falseConnection: connection.targetId
            };
          } else if (connection.sourceHandle === 'bullish') {
            return {
              ...block,
              bullishConnection: connection.targetId
            };
          } else if (connection.sourceHandle === 'bearish') {
            return {
              ...block,
              bearishConnection: connection.targetId
            };
          } else if (connection.sourceHandle === 'neutral') {
            return {
              ...block,
              neutralConnection: connection.targetId
            };
          }
        }

        // Update target block
        if (block.id === connection.targetId && connection.targetHandle === 'input') {
          return {
            ...block,
            inputConnections: [...(block.inputConnections || []), connection.sourceId]
          };
        }

        return block;
      });
    });

    setHasChanges(true);
    return true;
  }, [connections]);

  // Remove a connection
  const removeConnection = useCallback((connection: Connection) => {
    // Remove the connection
    setConnections(prevConnections =>
      prevConnections.filter(conn =>
        !(conn.sourceId === connection.sourceId &&
          conn.targetId === connection.targetId &&
          conn.sourceHandle === connection.sourceHandle &&
          conn.targetHandle === connection.targetHandle)
      )
    );

    // Update the blocks to reflect the removed connection
    setBlocks(prevBlocks => {
      return prevBlocks.map(block => {
        // Update source block
        if (block.id === connection.sourceId) {
          if (connection.sourceHandle === 'output') {
            return {
              ...block,
              outputConnections: (block.outputConnections || []).filter(id => id !== connection.targetId)
            };
          } else if (connection.sourceHandle === 'true') {
            return {
              ...block,
              trueConnection: undefined
            };
          } else if (connection.sourceHandle === 'false') {
            return {
              ...block,
              falseConnection: undefined
            };
          } else if (connection.sourceHandle === 'bullish') {
            return {
              ...block,
              bullishConnection: undefined
            };
          } else if (connection.sourceHandle === 'bearish') {
            return {
              ...block,
              bearishConnection: undefined
            };
          } else if (connection.sourceHandle === 'neutral') {
            return {
              ...block,
              neutralConnection: undefined
            };
          }
        }

        // Update target block
        if (block.id === connection.targetId && connection.targetHandle === 'input') {
          return {
            ...block,
            inputConnections: (block.inputConnections || []).filter(id => id !== connection.sourceId)
          };
        }

        return block;
      });
    });

    setHasChanges(true);
  }, []);

  // Set the entry block
  const setEntryBlock = useCallback((id: string) => {
    if (blocks.some(block => block.id === id)) {
      setEntryBlockId(id);
      setHasChanges(true);
      return true;
    }
    return false;
  }, [blocks]);

  // Clear the builder
  const clearBuilder = useCallback(() => {
    // Create a new "When Run" block
    const whenRunBlock = createDefaultWhenRunBlock();
    setBlocks([whenRunBlock]);
    setConnections([]);
    setEntryBlockId(whenRunBlock.id);
    setHasChanges(true);
  }, []);

  // Load an agent
  const loadAgent = useCallback((agent: Agent) => {
    // Ensure there's a "When Run" block
    let agentBlocks = agent.configuration.blocks || [];
    let agentEntryBlockId = agent.configuration.entryBlockId || '';

    // Check if there's a "When Run" block
    const hasWhenRunBlock = agentBlocks.some(block => block.type === BlockType.WHEN_RUN);

    // If not, add one
    if (!hasWhenRunBlock) {
      const whenRunBlock = createDefaultWhenRunBlock();
      agentBlocks = [whenRunBlock, ...agentBlocks];

      // If there's no entry block, set the "When Run" block as the entry
      if (!agentEntryBlockId) {
        agentEntryBlockId = whenRunBlock.id;
      }
    }

    setBlocks(agentBlocks);
    setEntryBlockId(agentEntryBlockId);
    setAgentName(agent.name || 'New Agent');
    setAgentDescription(agent.description || '');

    // Reconstruct connections from block data
    const newConnections: Connection[] = [];

    agentBlocks.forEach(block => {
      // Add output connections
      if (block.outputConnections) {
        block.outputConnections.forEach(targetId => {
          newConnections.push({
            sourceId: block.id,
            targetId,
            sourceHandle: 'output',
            targetHandle: 'input'
          });
        });
      }

      // Add true/false connections for condition blocks
      if (block.type === BlockType.CONDITION) {
        if (block.trueConnection) {
          newConnections.push({
            sourceId: block.id,
            targetId: block.trueConnection,
            sourceHandle: 'true',
            targetHandle: 'input'
          });
        }

        if (block.falseConnection) {
          newConnections.push({
            sourceId: block.id,
            targetId: block.falseConnection,
            sourceHandle: 'false',
            targetHandle: 'input'
          });
        }
      }

      // Add connections for candle pattern blocks
      if (block.type === BlockType.CANDLE_PATTERN) {
        if (block.trueConnection) {
          newConnections.push({
            sourceId: block.id,
            targetId: block.trueConnection,
            sourceHandle: 'true',
            targetHandle: 'input'
          });
        }

        if (block.falseConnection) {
          newConnections.push({
            sourceId: block.id,
            targetId: block.falseConnection,
            sourceHandle: 'false',
            targetHandle: 'input'
          });
        }

        if (block.bullishConnection) {
          newConnections.push({
            sourceId: block.id,
            targetId: block.bullishConnection,
            sourceHandle: 'bullish',
            targetHandle: 'input'
          });
        }

        if (block.bearishConnection) {
          newConnections.push({
            sourceId: block.id,
            targetId: block.bearishConnection,
            sourceHandle: 'bearish',
            targetHandle: 'input'
          });
        }

        if (block.neutralConnection) {
          newConnections.push({
            sourceId: block.id,
            targetId: block.neutralConnection,
            sourceHandle: 'neutral',
            targetHandle: 'input'
          });
        }
      }
    });

    setConnections(newConnections);
  }, []);

  // Clean up orphaned connections
  const cleanupOrphanedConnections = useCallback((agentBlocks: AgentBlock[]) => {
    const blockIds = new Set(agentBlocks.map(block => block.id));

    return agentBlocks.map(block => {
      const cleanedBlock = { ...block };

      // Clean up inputConnections
      if (cleanedBlock.inputConnections) {
        cleanedBlock.inputConnections = cleanedBlock.inputConnections.filter(id => blockIds.has(id));
      }

      // Clean up outputConnections
      if (cleanedBlock.outputConnections) {
        cleanedBlock.outputConnections = cleanedBlock.outputConnections.filter(id => blockIds.has(id));
      }

      // Clean up trueConnection
      if (cleanedBlock.trueConnection && !blockIds.has(cleanedBlock.trueConnection)) {
        cleanedBlock.trueConnection = undefined;
      }

      // Clean up falseConnection
      if (cleanedBlock.falseConnection && !blockIds.has(cleanedBlock.falseConnection)) {
        cleanedBlock.falseConnection = undefined;
      }

      return cleanedBlock;
    });
  }, []);

  // Get the agent configuration
  const getAgentConfiguration = useCallback((): Agent => {
    // Clean up any orphaned connections before returning the configuration
    const cleanedBlocks = cleanupOrphanedConnections(blocks);

    return {
      name: agentName,
      description: agentDescription,
      configuration: {
        blocks: cleanedBlocks,
        entryBlockId
      }
    };
  }, [blocks, entryBlockId, agentName, agentDescription, cleanupOrphanedConnections]);

  // Validate the agent configuration
  const validateAgent = useCallback((): {
    valid: boolean;
    errors: string[];
    errorDetails: Record<string, string[]>;
    disconnectedBlocks: AgentBlock[];
  } => {
    const errors: string[] = [];
    const errorDetails: Record<string, string[]> = {};

    // Check if there are any blocks
    if (blocks.length === 0) {
      errors.push('Agent must have at least one block');
      return { valid: false, errors, errorDetails, disconnectedBlocks: [] };
    }

    // Check if there is an entry block
    if (!entryBlockId) {
      errors.push('Agent must have an entry block');
      return { valid: false, errors, errorDetails, disconnectedBlocks: [] };
    }

    // Check if the entry block exists
    if (!blocks.some(block => block.id === entryBlockId)) {
      errors.push('Entry block does not exist');
      return { valid: false, errors, errorDetails, disconnectedBlocks: [] };
    }

    // Check if there is at least one trigger block
    const triggerBlocks = blocks.filter(block => block.type === BlockType.TRIGGER);
    if (triggerBlocks.length === 0) {
      errors.push('Agent must have at least one trigger block');
      return { valid: false, errors, errorDetails, disconnectedBlocks: [] };
    }

    // Check if all trigger blocks are connected
    const connectedTriggers = triggerBlocks.filter(block =>
      block.inputConnections && block.inputConnections.length > 0
    );

    if (connectedTriggers.length === 0 && triggerBlocks.length > 0) {
      errors.push('At least one trigger block must be connected to other blocks');
      triggerBlocks.forEach(block => {
        if (!errorDetails[block.id]) errorDetails[block.id] = [];
        errorDetails[block.id].push('Trigger block is not connected to any input');
      });
    }

    // Check if all blocks are connected
    const connectedBlockIds = new Set<string>();
    connectedBlockIds.add(entryBlockId);

    // Helper function to recursively find connected blocks
    const findConnectedBlocks = (blockId: string) => {
      const block = blocks.find(b => b.id === blockId);
      if (!block) return;

      // Add output connections
      if (block.outputConnections) {
        block.outputConnections.forEach(targetId => {
          if (!connectedBlockIds.has(targetId)) {
            connectedBlockIds.add(targetId);
            findConnectedBlocks(targetId);
          }
        });
      }

      // Add true/false connections for condition blocks
      if (block.type === BlockType.CONDITION) {
        if (block.trueConnection && !connectedBlockIds.has(block.trueConnection)) {
          connectedBlockIds.add(block.trueConnection);
          findConnectedBlocks(block.trueConnection);
        }

        if (block.falseConnection && !connectedBlockIds.has(block.falseConnection)) {
          connectedBlockIds.add(block.falseConnection);
          findConnectedBlocks(block.falseConnection);
        }
      }

      // Add connections for candle pattern blocks
      if (block.type === BlockType.CANDLE_PATTERN) {
        if (block.trueConnection && !connectedBlockIds.has(block.trueConnection)) {
          connectedBlockIds.add(block.trueConnection);
          findConnectedBlocks(block.trueConnection);
        }

        if (block.falseConnection && !connectedBlockIds.has(block.falseConnection)) {
          connectedBlockIds.add(block.falseConnection);
          findConnectedBlocks(block.falseConnection);
        }

        if (block.bullishConnection && !connectedBlockIds.has(block.bullishConnection)) {
          connectedBlockIds.add(block.bullishConnection);
          findConnectedBlocks(block.bullishConnection);
        }

        if (block.bearishConnection && !connectedBlockIds.has(block.bearishConnection)) {
          connectedBlockIds.add(block.bearishConnection);
          findConnectedBlocks(block.bearishConnection);
        }

        if (block.neutralConnection && !connectedBlockIds.has(block.neutralConnection)) {
          connectedBlockIds.add(block.neutralConnection);
          findConnectedBlocks(block.neutralConnection);
        }
      }
    };

    findConnectedBlocks(entryBlockId);

    const disconnectedBlocks = blocks.filter(block => !connectedBlockIds.has(block.id));

    // Add specific error messages for disconnected blocks
    disconnectedBlocks.forEach(block => {
      if (!errorDetails[block.id]) errorDetails[block.id] = [];
      errorDetails[block.id].push('Block is disconnected from the flow');
    });

    if (disconnectedBlocks.length > 0) {
      errors.push(`There are ${disconnectedBlocks.length} disconnected blocks`);
    }

    // Check if condition blocks have at least a true connection (false connection is optional)
    const conditionBlocks = blocks.filter(block => block.type === BlockType.CONDITION);
    conditionBlocks.forEach(block => {
      if (connectedBlockIds.has(block.id)) { // Only check connected condition blocks
        if (!block.trueConnection) {
          if (!errorDetails[block.id]) errorDetails[block.id] = [];
          errorDetails[block.id].push('Condition block is missing TRUE output connection');
          errors.push(`Condition block is missing TRUE output connection`);
        }

        // FALSE connection is now optional - no validation required
        // This allows for conditions where you only want to do something when true
      }
    });

    return {
      valid: errors.length === 0,
      errors,
      errorDetails,
      disconnectedBlocks
    };
  }, [blocks, entryBlockId]);

  // Custom setters that track changes
  const setAgentNameWithTracking = useCallback((name: string) => {
    setAgentName(name);
    setHasChanges(true);
  }, []);

  const setAgentDescriptionWithTracking = useCallback((description: string) => {
    setAgentDescription(description);
    setHasChanges(true);
  }, []);

  // Delete selected connections
  const deleteSelectedConnections = useCallback((selectedConnectionIds: string[]) => {
    if (selectedConnectionIds.length === 0) return;

    // Save current state to history before making changes
    saveToHistory();

    // Remove selected connections
    setConnections(prevConnections =>
      prevConnections.filter((_, index) =>
        !selectedConnectionIds.includes(`e-${index}`)
      )
    );

    // Update blocks to reflect removed connections
    selectedConnectionIds.forEach(edgeId => {
      const edgeIndex = parseInt(edgeId.replace('e-', ''));
      const connection = connections[edgeIndex];

      if (connection) {
        // Update blocks to remove connection references
        setBlocks(prevBlocks => {
          return prevBlocks.map(block => {
            // Update source block
            if (block.id === connection.sourceId) {
              if (connection.sourceHandle === 'output') {
                return {
                  ...block,
                  outputConnections: (block.outputConnections || []).filter(id => id !== connection.targetId)
                };
              } else if (connection.sourceHandle === 'true') {
                return {
                  ...block,
                  trueConnection: undefined
                };
              } else if (connection.sourceHandle === 'false') {
                return {
                  ...block,
                  falseConnection: undefined
                };
              } else if (connection.sourceHandle === 'bullish') {
                return {
                  ...block,
                  bullishConnection: undefined
                };
              } else if (connection.sourceHandle === 'bearish') {
                return {
                  ...block,
                  bearishConnection: undefined
                };
              } else if (connection.sourceHandle === 'neutral') {
                return {
                  ...block,
                  neutralConnection: undefined
                };
              }
            }

            // Update target block
            if (block.id === connection.targetId && connection.targetHandle === 'input') {
              return {
                ...block,
                inputConnections: (block.inputConnections || []).filter(id => id !== connection.sourceId)
              };
            }

            return block;
          });
        });
      }
    });

    setHasChanges(true);
  }, [connections, saveToHistory]);

  return {
    blocks,
    connections,
    entryBlockId,
    agentName,
    agentDescription,
    setAgentName: setAgentNameWithTracking,
    setAgentDescription: setAgentDescriptionWithTracking,
    addBlock,
    updateBlock,
    removeBlock,
    addConnection,
    removeConnection,
    setEntryBlock,
    clearBuilder,
    loadAgent,
    getAgentConfiguration,
    validateAgent,
    hasChanges,
    resetChanges,
    // Undo/Redo functionality
    undo,
    redo,
    canUndo,
    canRedo,
    // Connection deletion
    deleteSelectedConnections
  };
}
