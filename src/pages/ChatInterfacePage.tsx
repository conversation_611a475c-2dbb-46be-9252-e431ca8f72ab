import React, { useState, useEffect, useRef } from 'react';
import { Send } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface Message {
  role: 'user' | 'assistant';
  content: string;
  isLoading?: boolean;
  loadingStage?: 'loading' | 'fetching';
}

// Typing animation component for cycling prompts
const TypingPlaceholder: React.FC<{ examples: string[] }> = ({ examples }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [displayText, setDisplayText] = useState('');
  const [isTyping, setIsTyping] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const currentExample = examples[currentIndex];
    const typeSpeed = 80;
    const deleteSpeed = 40;
    const pauseTime = 2000;

    let timeout: NodeJS.Timeout;

    if (isTyping && !isDeleting) {
      if (displayText.length < currentExample.length) {
        timeout = setTimeout(() => {
          setDisplayText(currentExample.slice(0, displayText.length + 1));
        }, typeSpeed);
      } else {
        timeout = setTimeout(() => {
          setIsDeleting(true);
          setIsTyping(false);
        }, pauseTime);
      }
    } else if (isDeleting && !isTyping) {
      if (displayText.length > 0) {
        timeout = setTimeout(() => {
          setDisplayText(displayText.slice(0, -1));
        }, deleteSpeed);
      } else {
        setIsDeleting(false);
        setCurrentIndex((prev) => (prev + 1) % examples.length);
        setTimeout(() => {
          setIsTyping(true);
        }, 300);
      }
    }

    return () => clearTimeout(timeout);
  }, [displayText, isTyping, isDeleting, currentIndex, examples]);

  return (
    <span className="text-white/40">
      {displayText}
      <span className="animate-pulse">|</span>
    </span>
  );
};

const ChatInterfacePage: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { isAuthenticated, user } = useAuth();

  const placeholderExamples = [
    "What are we analyzing, Cale?",
    "Scan PLTR using my RSI Agent",
    "Show me portfolio performance",
    "Analyze market trends today",
    "Build a new trading agent"
  ];

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSend = async () => {
    if (input.trim() === '' || isLoading) return;

    const newMessage: Message = { role: 'user', content: input };
    setMessages((prevMessages) => [...prevMessages, newMessage]);
    setInput('');
    setIsLoading(true);

    // Add loading message
    setMessages((prevMessages) => [
      ...prevMessages,
      { role: 'assistant', content: '', isLoading: true, loadingStage: 'loading' }
    ]);

    try {
      if (!isAuthenticated || !user) {
        throw new Error('User not authenticated.');
      }

      // Simulate first stage delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update to fetching stage
      setMessages((prevMessages) =>
        prevMessages.map(msg =>
          msg.isLoading ? { ...msg, loadingStage: 'fetching' } : msg
        )
      );

      // Simulate second stage delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Remove loading message and add response
      setMessages((prevMessages) => {
        const withoutLoading = prevMessages.filter(msg => !msg.isLoading);
        return [
          ...withoutLoading,
          { role: 'assistant', content: 'This is a placeholder response. Chat functionality will be implemented.' }
        ];
      });
    } catch (error) {
      console.error('Error sending message:', error);
      setMessages((prevMessages) => {
        const withoutLoading = prevMessages.filter(msg => !msg.isLoading);
        return [
          ...withoutLoading,
          { role: 'assistant', content: 'Sorry, something went wrong.' }
        ];
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="px-8 py-6 border-b border-white/[0.06]">
        <h1 className="text-2xl font-medium text-white font-sans tracking-tight">
          What are we analyzing, Cale?
        </h1>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto px-8 py-6">
        <div className="max-w-4xl mx-auto space-y-8">
          {messages.map((message, index) => (
            <div key={index} className="space-y-2">
              {message.role === 'user' ? (
                <div className="text-left">
                  <div className="text-white text-lg leading-relaxed">
                    {message.content}
                  </div>
                </div>
              ) : (
                <div className="text-left">
                  {message.isLoading ? (
                    <div className="flex items-center gap-3 text-white/60">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-white/40 rounded-full animate-pulse"></div>
                        <div className="w-2 h-2 bg-white/40 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                        <div className="w-2 h-2 bg-white/40 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                      </div>
                      <span className="text-sm">
                        {message.loadingStage === 'loading' ? 'Loading' : 'Fetching'}
                      </span>
                    </div>
                  ) : (
                    <div className="text-white/80 text-lg leading-relaxed">
                      {message.content}
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input */}
      <div className="px-8 py-6 border-t border-white/[0.06]">
        <div className="max-w-4xl mx-auto relative">
          <input
            type="text"
            className="w-full bg-transparent border-none outline-none text-white text-lg placeholder-white/40 resize-none"
            placeholder=""
            value={input}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={isLoading}
          />

          {/* Animated placeholder when empty */}
          {!input && !isLoading && (
            <div className="absolute inset-0 pointer-events-none flex items-center">
              <TypingPlaceholder examples={placeholderExamples} />
            </div>
          )}

          {/* Send button */}
          {input.trim() && (
            <button
              onClick={handleSend}
              disabled={isLoading}
              className="absolute right-0 top-1/2 -translate-y-1/2 p-2 text-white/60 hover:text-white transition-colors disabled:opacity-50"
            >
              <Send className="w-5 h-5" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatInterfacePage;