import React, { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { WelcomeHeading } from '@/components/ui/WelcomeHeading';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Heart, 
  Play, 
  Users, 
  TrendingUp, 
  Search,
  Filter,
  Sparkles,
  Target,
  Bot
} from 'lucide-react';
import { 
  getPublicAgents, 
  toggleAgentLike, 
  hasUserLikedAgent,
  incrementAgentUsage,
  Agent 
} from '@/services/agentService';
import { useNavigate } from 'react-router-dom';

interface PublicAgent extends Agent {
  profiles?: {
    username?: string;
    full_name?: string;
  };
  isLiked?: boolean;
}

const Discovery: React.FC = () => {
  const [agents, setAgents] = useState<PublicAgent[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTag, setSelectedTag] = useState<string>('');
  const { toast } = useToast();
  const navigate = useNavigate();

  // Load public agents
  const loadPublicAgents = async () => {
    try {
      setLoading(true);
      const publicAgents = await getPublicAgents();
      
      // Check which agents the user has liked
      const agentsWithLikes = await Promise.all(
        publicAgents.map(async (agent) => {
          const isLiked = await hasUserLikedAgent(agent.id!);
          return { ...agent, isLiked };
        })
      );
      
      setAgents(agentsWithLikes);
    } catch (error) {
      console.error('Error loading public agents:', error);
      toast({
        title: 'Error',
        description: 'Failed to load public agents',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPublicAgents();
  }, []);

  // Handle like/unlike
  const handleLike = async (agentId: string) => {
    try {
      const result = await toggleAgentLike(agentId);
      
      // Update local state
      setAgents(prev => prev.map(agent => 
        agent.id === agentId 
          ? { 
              ...agent, 
              isLiked: result.liked,
              likes_count: (agent.likes_count || 0) + (result.liked ? 1 : -1)
            }
          : agent
      ));

      toast({
        title: result.liked ? 'Agent liked!' : 'Agent unliked',
        description: result.liked ? 'Added to your favorites' : 'Removed from favorites'
      });
    } catch (error) {
      console.error('Error toggling like:', error);
      toast({
        title: 'Error',
        description: 'Failed to update like status',
        variant: 'destructive'
      });
    }
  };

  // Handle use agent
  const handleUseAgent = async (agent: PublicAgent) => {
    try {
      // Increment usage count
      await incrementAgentUsage(agent.id!);
      
      // Navigate to agent builder with the agent configuration
      navigate('/agent-builder', { 
        state: { 
          importedAgent: {
            name: `${agent.name} (Copy)`,
            description: agent.description,
            configuration: agent.configuration
          }
        }
      });

      toast({
        title: 'Agent imported!',
        description: 'You can now customize and use this agent'
      });
    } catch (error) {
      console.error('Error using agent:', error);
      toast({
        title: 'Error',
        description: 'Failed to import agent',
        variant: 'destructive'
      });
    }
  };

  // Get all unique tags
  const allTags = Array.from(
    new Set(agents.flatMap(agent => agent.tags || []))
  ).sort();

  // Filter agents based on search and tag
  const filteredAgents = agents.filter(agent => {
    const matchesSearch = !searchTerm || 
      agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      agent.public_description?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesTag = !selectedTag || 
      (agent.tags && agent.tags.includes(selectedTag));
    
    return matchesSearch && matchesTag;
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0A0A0A] text-white">
        <div className="container mx-auto p-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Bot className="h-12 w-12 text-white/40 mx-auto mb-4 animate-pulse" />
              <p className="text-white/60">Loading public agents...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white">
      <div className="container mx-auto p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <WelcomeHeading
              text="Discovery"
              className="gradient-text text-2xl font-medium font-sans tracking-tight leading-tight"
              speed={80}
            />
            <Badge variant="secondary" className="bg-white/5 text-white/60">
              {filteredAgents.length} agents
            </Badge>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/40" />
            <input
              type="text"
              placeholder="Search agents..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-[#141414]/40 border border-[#1A1A1A]/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-white/20"
            />
          </div>
          
          {allTags.length > 0 && (
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-white/40" />
              <select
                value={selectedTag}
                onChange={(e) => setSelectedTag(e.target.value)}
                className="bg-[#141414]/40 border border-[#1A1A1A]/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-white/20"
              >
                <option value="">All tags</option>
                {allTags.map(tag => (
                  <option key={tag} value={tag}>{tag}</option>
                ))}
              </select>
            </div>
          )}
        </div>

        {/* Agents Grid */}
        {filteredAgents.length === 0 ? (
          <div className="text-center py-16">
            <Target className="h-16 w-16 text-white/20 mx-auto mb-6" />
            <h3 className="text-xl font-medium text-white/80 mb-2">
              {agents.length === 0 ? 'No Public Agents Yet' : 'No Agents Found'}
            </h3>
            <p className="text-white/40 mb-6 max-w-md mx-auto">
              {agents.length === 0 
                ? 'Be the first to share your trading agent with the community! Create an agent and make it public in the Agent Builder.'
                : 'Try adjusting your search terms or filters to find more agents.'
              }
            </p>
            {agents.length === 0 && (
              <Button
                onClick={() => navigate('/agent-builder')}
                className="bg-white/10 hover:bg-white/20 text-white border border-white/20"
              >
                <Sparkles className="h-4 w-4 mr-2" />
                Create Your First Agent
              </Button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredAgents.map((agent) => (
              <PublicAgentCard
                key={agent.id}
                agent={agent}
                onLike={() => handleLike(agent.id!)}
                onUse={() => handleUseAgent(agent)}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// Public Agent Card Component
interface PublicAgentCardProps {
  agent: PublicAgent;
  onLike: () => void;
  onUse: () => void;
}

const PublicAgentCard: React.FC<PublicAgentCardProps> = ({ agent, onLike, onUse }) => {
  const creatorName = agent.profiles?.full_name || agent.profiles?.username || 'Anonymous';
  
  return (
    <Card className="overflow-hidden bg-[#141414]/40 border border-[#1A1A1A]/20 hover:border-[#232323]/40 transition-all duration-200 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] backdrop-blur-sm group">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-white/90 text-lg truncate">{agent.name}</CardTitle>
            <p className="text-white/40 text-sm mt-1">by {creatorName}</p>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onLike}
            className={`h-8 w-8 transition-colors ${
              agent.isLiked 
                ? 'text-red-400 hover:text-red-300' 
                : 'text-white/40 hover:text-red-400'
            }`}
          >
            <Heart className={`h-4 w-4 ${agent.isLiked ? 'fill-current' : ''}`} />
          </Button>
        </div>
        
        {agent.public_description && (
          <p className="text-white/60 text-sm mt-2 line-clamp-2">
            {agent.public_description}
          </p>
        )}
        
        {agent.tags && agent.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-3">
            {agent.tags.slice(0, 3).map(tag => (
              <Badge key={tag} variant="secondary" className="bg-white/5 text-white/60 text-xs">
                {tag}
              </Badge>
            ))}
            {agent.tags.length > 3 && (
              <Badge variant="secondary" className="bg-white/5 text-white/60 text-xs">
                +{agent.tags.length - 3}
              </Badge>
            )}
          </div>
        )}
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="flex items-center justify-between text-sm text-white/40 mb-4">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <Heart className="h-3 w-3" />
              <span>{agent.likes_count || 0}</span>
            </div>
            <div className="flex items-center gap-1">
              <Users className="h-3 w-3" />
              <span>{agent.usage_count || 0}</span>
            </div>
            <div className="flex items-center gap-1">
              <Bot className="h-3 w-3" />
              <span>{agent.configuration.blocks.length} blocks</span>
            </div>
          </div>
        </div>
        
        <Button
          onClick={onUse}
          className="w-full bg-white/10 hover:bg-white/20 text-white border border-white/20 transition-all duration-200"
        >
          <Play className="h-4 w-4 mr-2" />
          Use Agent
        </Button>
      </CardContent>
    </Card>
  );
};

export default Discovery;
