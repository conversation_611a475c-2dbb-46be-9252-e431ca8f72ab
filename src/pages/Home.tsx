import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Hammer,
  Search,
  TestTube,
  CheckCircle,
  Clock
} from 'lucide-react';

const Home: React.FC = () => {
  const navigate = useNavigate();

  // Mock data - in real app this would come from API
  const hasAgents = false; // Set to true when user has agents
  const hasCompletedActions = false; // Set to true when user has completed actions

  const accomplishments = hasCompletedActions ? [
    { title: 'First Agent Created', completed: true, date: '2 days ago' },
    { title: 'Portfolio Optimized', completed: true, date: '1 day ago' },
    { title: 'Market Scanner Expert', completed: false, date: null }
  ] : [];

  const agentPerformance = hasAgents ? [
    { name: 'Growth Agent Alpha', performance: '+12.4%', trades: 23 },
    { name: 'Value Hunter Beta', performance: '+8.7%', trades: 15 }
  ] : [];

  return (
    <div className="h-full bg-[#0A0A0A] text-white flex flex-col">
      {/* Navigator Buttons - Top Right */}
      <div className="absolute top-6 right-6 z-10 flex gap-3">
        <button
          onClick={() => navigate('/agent-builder')}
          className="flex items-center gap-2 bg-white hover:bg-white/95 text-black px-4 py-2 rounded-lg text-sm font-medium shadow-sm hover:shadow-md transition-all duration-200 font-sans"
        >
          <Hammer className="w-4 h-4" />
          <span>Build</span>
        </button>
        <button
          onClick={() => navigate('/agent-backtesting')}
          className="flex items-center gap-2 bg-white/[0.08] hover:bg-white/[0.12] text-white px-4 py-2 rounded-lg text-sm font-medium border border-white/[0.08] transition-all duration-200 font-sans"
        >
          <TestTube className="w-4 h-4" />
          <span>Test</span>
        </button>
        <button
          onClick={() => navigate('/agent-scanner')}
          className="flex items-center gap-2 bg-white/[0.08] hover:bg-white/[0.12] text-white px-4 py-2 rounded-lg text-sm font-medium border border-white/[0.08] transition-all duration-200 font-sans"
        >
          <Search className="w-4 h-4" />
          <span>Scan</span>
        </button>
      </div>

      <div className="flex-1 max-w-6xl mx-auto px-8 py-12 flex flex-col">
        {/* Header */}
        <div className="mb-12">
          <h1 className="text-4xl font-normal text-white mb-4 font-sans">
            Trading Headquarters
          </h1>
          <p className="text-white/60 text-lg font-sans">
            Welcome back to your trading command center
          </p>
        </div>

        {/* Main Content Grid */}
        <div className="flex-1 grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Left Column */}
          <div className="space-y-8">
            {/* Recent Activity */}
            <div>
              <h2 className="text-xl font-medium text-white mb-6 font-sans">Recent Activity</h2>
              {hasCompletedActions ? (
                <div className="space-y-3">
                  {accomplishments.map((achievement, index) => (
                    <div key={index} className="flex items-center justify-between py-3 border-b border-white/[0.06] last:border-b-0">
                      <div className="flex items-center gap-3">
                        {achievement.completed ? (
                          <CheckCircle className="w-4 h-4 text-white/60" />
                        ) : (
                          <Clock className="w-4 h-4 text-white/40" />
                        )}
                        <span className="text-white font-sans">{achievement.title}</span>
                      </div>
                      <span className="text-white/60 text-sm font-sans">{achievement.date || 'Pending'}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <img
                    src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons/dashboard.svg"
                    alt="Dashboard"
                    className="w-8 h-8 mx-auto mb-3 opacity-20"
                  />
                  <p className="text-white/60 font-sans">No activity yet. Start by creating your first agent.</p>
                </div>
              )}
            </div>

            {/* Agent Performance */}
            <div>
              <h2 className="text-xl font-medium text-white mb-6 font-sans">Agent Performance</h2>
              {hasAgents ? (
                <div className="space-y-3">
                  {agentPerformance.map((agent, index) => (
                    <div key={index} className="flex items-center justify-between py-3 border-b border-white/[0.06] last:border-b-0">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 rounded-full bg-white/60"></div>
                        <span className="text-white font-sans">{agent.name}</span>
                        <span className="text-white/60 text-sm font-sans">({agent.trades} trades)</span>
                      </div>
                      <span className="text-white font-sans">{agent.performance}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <img
                    src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons/Bar%20chart.svg"
                    alt="Bar Chart"
                    className="w-8 h-8 mx-auto mb-3 opacity-20"
                  />
                  <p className="text-white/60 font-sans">No agents created yet. Build your first trading agent.</p>
                </div>
              )}
            </div>
          </div>

          {/* Right Column - Get Started */}
          <div>
            <h2 className="text-xl font-medium text-white mb-6 font-sans">Get Started</h2>
            <div className="space-y-4">
              <button
                onClick={() => navigate('/agent-builder')}
                className="w-full flex items-center gap-4 p-4 border border-white/[0.06] rounded-lg hover:border-white/[0.12] transition-all duration-200 text-left group"
              >
                <img
                  src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons/dashboard.svg"
                  alt="Build"
                  className="w-6 h-6 opacity-60 group-hover:opacity-80 transition-opacity duration-200"
                />
                <div>
                  <h3 className="text-white font-medium font-sans mb-1">Create Your First Agent</h3>
                  <p className="text-white/60 text-sm font-sans">Build an AI trading agent tailored to your strategy</p>
                </div>
              </button>

              <button
                onClick={() => navigate('/agent-scanner')}
                className="w-full flex items-center gap-4 p-4 border border-white/[0.06] rounded-lg hover:border-white/[0.12] transition-all duration-200 text-left group"
              >
                <img
                  src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons/Scan.svg"
                  alt="Scan"
                  className="w-6 h-6 opacity-60 group-hover:opacity-80 transition-opacity duration-200"
                />
                <div>
                  <h3 className="text-white font-medium font-sans mb-1">Run Market Scan</h3>
                  <p className="text-white/60 text-sm font-sans">Discover opportunities across thousands of stocks</p>
                </div>
              </button>

              <button
                onClick={() => navigate('/agent-backtesting')}
                className="w-full flex items-center gap-4 p-4 border border-white/[0.06] rounded-lg hover:border-white/[0.12] transition-all duration-200 text-left group"
              >
                <img
                  src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons/Bar%20chart.svg"
                  alt="Bar Chart"
                  className="w-6 h-6 opacity-60 group-hover:opacity-80 transition-opacity duration-200"
                />
                <div>
                  <h3 className="text-white font-medium font-sans mb-1">Test Strategy</h3>
                  <p className="text-white/60 text-sm font-sans">Backtest your ideas with historical data</p>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
