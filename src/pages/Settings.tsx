import React, { useState, useEffect } from 'react';
import { Save, User, Mail, Lock, ChevronDown, Check, AlertCircle, Eye, EyeOff } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';

const Settings = () => {
  const { toast } = useToast();
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [activeSection, setActiveSection] = useState('profile');
  const [hasChanges, setHasChanges] = useState(false);
  const [originalFullName, setOriginalFullName] = useState('');

  // Password change states
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);

  useEffect(() => {
    const fetchUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        setUser(user);
        setEmail(user.email || '');

        // Fetch profile data if available
        const { data: profile } = await supabase
          .from('profiles')
          .select('full_name')
          .eq('id', user.id)
          .single();

        if (profile) {
          setFullName(profile.full_name || '');
          setOriginalFullName(profile.full_name || '');
        }
      }
    };

    fetchUser();
  }, []);

  // Track changes
  useEffect(() => {
    setHasChanges(fullName !== originalFullName);
  }, [fullName, originalFullName]);

  // Password strength calculator
  const calculatePasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength += 25;
    if (password.match(/[a-z]/)) strength += 25;
    if (password.match(/[A-Z]/)) strength += 25;
    if (password.match(/[0-9]/)) strength += 25;
    return strength;
  };

  useEffect(() => {
    setPasswordStrength(calculatePasswordStrength(newPassword));
  }, [newPassword]);

  const handleSaveChanges = async () => {
    setLoading(true);
    try {
      // Update profile in database
      const { error } = await supabase
        .from('profiles')
        .upsert({
          id: user.id,
          full_name: fullName,
          updated_at: new Date()
        });

      if (error) throw error;

      setOriginalFullName(fullName);
      setHasChanges(false);

      toast({
        title: "Profile Updated",
        description: "Your profile has been successfully updated.",
        duration: 3000,
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update profile. Please try again.",
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    setPasswordError('');

    if (newPassword !== confirmPassword) {
      setPasswordError("New passwords don't match");
      return;
    }

    if (newPassword.length < 8) {
      setPasswordError("Password must be at least 8 characters");
      return;
    }

    if (passwordStrength < 75) {
      setPasswordError('Password is too weak. Please include uppercase, lowercase, and numbers.');
      return;
    }

    setLoading(true);
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (error) throw error;

      // Reset form and show success
      setNewPassword('');
      setConfirmPassword('');
      setShowPasswordForm(false);

      toast({
        title: "Password Updated",
        description: "Your password has been successfully changed.",
        duration: 3000,
      });
    } catch (error: any) {
      setPasswordError(error.message || "Failed to change password");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#0A0A0A]">
      <div className="max-w-lg mx-auto px-6 pt-16 pb-20">
        {/* Clean Header */}
        <div className="mb-12">
          <h1 className="text-2xl font-normal text-white mb-2 font-sans">Account Settings</h1>
          <p className="text-white/50 text-sm font-sans">Manage your profile and security</p>
        </div>

        {/* Simple Section Tabs */}
        <div className="flex gap-1 mb-8 p-1 bg-white/[0.02] rounded-lg border border-white/[0.04]">
          <button
            onClick={() => setActiveSection('profile')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-sans transition-all duration-200 ${
              activeSection === 'profile'
                ? 'bg-white/[0.08] text-white border border-white/[0.08]'
                : 'text-white/60 hover:text-white/80'
            }`}
          >
            Profile
          </button>
          <button
            onClick={() => setActiveSection('security')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-sans transition-all duration-200 ${
              activeSection === 'security'
                ? 'bg-white/[0.08] text-white border border-white/[0.08]'
                : 'text-white/60 hover:text-white/80'
            }`}
          >
            Security
          </button>
        </div>

        {/* Content Area */}
        <div className="space-y-6">

          {activeSection === 'profile' && (
            <div className="space-y-6">
              {/* Full Name */}
              <div className="space-y-2">
                <label className="text-sm text-white/70 font-sans">Full Name</label>
                <Input
                  type="text"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  className="bg-transparent border border-white/20 rounded-lg text-white placeholder:text-white/40 focus:border-white/40 focus:ring-0 transition-colors duration-200 font-sans"
                  placeholder="Enter your full name"
                />
              </div>

              {/* Email */}
              <div className="space-y-2">
                <label className="text-sm text-white/70 font-sans">Email Address</label>
                <Input
                  type="email"
                  value={email}
                  disabled
                  className="bg-transparent border border-white/10 rounded-lg text-white/50 cursor-not-allowed font-sans"
                />
                <p className="text-xs text-white/40 font-sans">Email cannot be changed</p>
              </div>

              {/* Save Button */}
              <button
                onClick={handleSaveChanges}
                disabled={loading || !hasChanges}
                className={`w-full py-2.5 rounded-lg text-sm font-medium transition-all duration-200 font-sans ${
                  hasChanges
                    ? 'bg-white hover:bg-gray-50 text-black shadow-[inset_0_1px_2px_rgba(0,0,0,0.05),0_1px_3px_rgba(0,0,0,0.1)]'
                    : 'bg-white/[0.03] text-white/40 cursor-not-allowed border border-white/[0.08]'
                }`}
              >
                {loading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin inline-block mr-2" />
                    Saving...
                  </>
                ) : hasChanges ? (
                  'Save Changes'
                ) : (
                  'Saved'
                )}
              </button>
            </div>
          )}

          {activeSection === 'security' && (
            <div className="space-y-6">
              {!showPasswordForm ? (
                <>
                  <p className="text-white/50 text-sm font-sans">Manage your account security and password</p>

                  <button
                    onClick={() => setShowPasswordForm(true)}
                    className="w-full py-2.5 rounded-lg text-sm bg-white/[0.03] hover:bg-white/[0.06] text-white/90 border border-white/[0.08] transition-colors duration-200 font-sans"
                  >
                    Change Password
                  </button>
                </>
              ) : (
                <form onSubmit={handlePasswordChange} className="space-y-4">
                  {/* New Password */}
                  <div className="space-y-2">
                    <label className="text-sm text-white/70 font-sans">New Password</label>
                    <div className="relative">
                      <Input
                        type={showNewPassword ? "text" : "password"}
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        className="bg-transparent border border-white/20 rounded-lg text-white placeholder:text-white/40 focus:border-white/40 focus:ring-0 transition-colors duration-200 pr-10 font-sans"
                        placeholder="Enter new password"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowNewPassword(!showNewPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/40 hover:text-white/60 transition-colors"
                      >
                        {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>

                    {/* Password Strength */}
                    {newPassword && (
                      <div className="space-y-1">
                        <div className="flex justify-between text-xs">
                          <span className="text-white/50 font-sans">Strength</span>
                          <span className={`font-sans ${
                            passwordStrength >= 75 ? 'text-emerald-400' :
                            passwordStrength >= 50 ? 'text-yellow-400' : 'text-red-400'
                          }`}>
                            {passwordStrength >= 75 ? 'Strong' :
                             passwordStrength >= 50 ? 'Medium' : 'Weak'}
                          </span>
                        </div>
                        <div className="w-full bg-white/10 rounded-full h-1">
                          <div
                            className={`h-1 rounded-full transition-all duration-300 ${
                              passwordStrength >= 75 ? 'bg-emerald-500' :
                              passwordStrength >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                            }`}
                            style={{ width: `${passwordStrength}%` }}
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Confirm Password */}
                  <div className="space-y-2">
                    <label className="text-sm text-white/70 font-sans">Confirm Password</label>
                    <div className="relative">
                      <Input
                        type={showConfirmPassword ? "text" : "password"}
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        className="bg-transparent border border-white/20 rounded-lg text-white placeholder:text-white/40 focus:border-white/40 focus:ring-0 transition-colors duration-200 pr-10 font-sans"
                        placeholder="Confirm new password"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/40 hover:text-white/60 transition-colors"
                      >
                        {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>

                  {passwordError && (
                    <p className="text-red-400 text-sm font-sans">{passwordError}</p>
                  )}

                  {/* Action Buttons */}
                  <div className="flex gap-3 pt-2">
                    <button
                      type="button"
                      onClick={() => {
                        setShowPasswordForm(false);
                        setPasswordError('');
                        setNewPassword('');
                        setConfirmPassword('');
                      }}
                      className="flex-1 py-2.5 rounded-lg text-sm bg-white/[0.03] hover:bg-white/[0.06] text-white/70 border border-white/[0.08] transition-colors duration-200 font-sans"
                    >
                      Cancel
                    </button>

                    <button
                      type="submit"
                      disabled={loading || passwordStrength < 75}
                      className={`flex-1 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 font-sans ${
                        passwordStrength >= 75 && newPassword === confirmPassword && newPassword.length >= 8
                          ? 'bg-white hover:bg-gray-50 text-black shadow-[inset_0_1px_2px_rgba(0,0,0,0.05),0_1px_3px_rgba(0,0,0,0.1)]'
                          : 'bg-white/[0.03] text-white/40 cursor-not-allowed border border-white/[0.08]'
                      }`}
                    >
                      {loading ? (
                        <>
                          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin inline-block mr-2" />
                          Updating...
                        </>
                      ) : (
                        'Update Password'
                      )}
                    </button>
                  </div>
                </form>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Settings;
