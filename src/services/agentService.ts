import { supabase } from '@/integrations/supabase/client';

// Types for the agent service
export interface AgentBlock {
  id: string;
  type: string;
  position: { x: number; y: number };
  [key: string]: any;
}

export interface Agent {
  id?: string;
  name: string;
  description?: string;
  configuration: {
    blocks: AgentBlock[];
    entryBlockId: string;
  };
  created_at?: string;
  updated_at?: string;
}

export interface AgentRun {
  id?: string;
  agent_id: string;
  symbol: string;
  result: {
    signal: 'bullish' | 'bearish' | 'neutral';
    confidence: number;
    reasoning: string;
    metrics: Record<string, any>;
    executionPath: string[];
    executionTime: number;
    timestamp: string;
  };
  created_at?: string;
}

/**
 * Check if an agent name already exists
 * @param name - The name to check
 * @param excludeId - Optional ID to exclude from the check
 * @returns True if the name exists, false otherwise
 */
export async function checkAgentNameExists(name: string, excludeId?: string): Promise<boolean> {
  try {
    let query = supabase
      .from('agents')
      .select('id')
      .eq('name', name);

    if (excludeId) {
      query = query.neq('id', excludeId);
    }

    const { data, error } = await query;

    if (error) throw error;
    return data && data.length > 0;
  } catch (error) {
    console.error('Error checking agent name:', error);
    return false;
  }
}

/**
 * Generate a unique name for an agent
 * @param baseName - The base name to use
 * @param excludeId - Optional ID to exclude from the check
 * @returns A unique name
 */
export async function generateUniqueName(baseName: string, excludeId?: string): Promise<string> {
  // Check if the base name is already unique
  const exists = await checkAgentNameExists(baseName, excludeId);
  if (!exists) return baseName;

  // Try adding numbers until we find a unique name
  let counter = 1;
  let uniqueName = `${baseName} (${counter})`;

  while (await checkAgentNameExists(uniqueName, excludeId)) {
    counter++;
    uniqueName = `${baseName} (${counter})`;

    // Safety check to prevent infinite loops
    if (counter > 100) {
      uniqueName = `${baseName} (${Date.now()})`;
      break;
    }
  }

  return uniqueName;
}

/**
 * Save an agent to Supabase
 * @param agent - The agent to save
 * @returns The saved agent
 */
export async function saveAgent(agent: Agent): Promise<Agent> {
  try {
    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    // Check if this is an update or a new agent
    if (agent.id) {
      // Update existing agent
      const { data, error } = await supabase
        .from('agents')
        .update({
          name: agent.name,
          description: agent.description,
          configuration: agent.configuration,
          updated_at: new Date().toISOString()
        })
        .eq('id', agent.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } else {
      // Generate a unique name if needed
      const uniqueName = await generateUniqueName(agent.name);

      // Create new agent
      const { data, error } = await supabase
        .from('agents')
        .insert({
          name: uniqueName,
          description: agent.description,
          configuration: agent.configuration,
          user_id: user.id
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    }
  } catch (error) {
    console.error('Error saving agent:', error);
    throw error;
  }
}

/**
 * Get all agents for the current user
 * @returns Array of agents
 */
export async function getAgents(): Promise<Agent[]> {
  try {
    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('agents')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting agents:', error);
    throw error;
  }
}

/**
 * Get all agents for a specific user ID
 * @param userId - The user ID to get agents for
 * @returns Array of agents
 */
export async function getAgentsByUserId(userId: string): Promise<Agent[]> {
  try {
    const { data, error } = await supabase
      .from('agents')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting agents by user ID:', error);
    throw error;
  }
}

/**
 * Get an agent by ID
 * @param id - The agent ID
 * @returns The agent
 */
export async function getAgentById(id: string): Promise<Agent | null> {
  try {
    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('agents')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error(`Error getting agent with ID ${id}:`, error);
    throw error;
  }
}

/**
 * Delete an agent
 * @param id - The agent ID
 * @returns Success status
 */
export async function deleteAgent(id: string): Promise<boolean> {
  try {
    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    const { error } = await supabase
      .from('agents')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error(`Error deleting agent with ID ${id}:`, error);
    throw error;
  }
}

/**
 * Run an agent
 * @param agentId - The agent ID
 * @param symbol - The symbol to analyze
 * @param timeframe - The timeframe to analyze
 * @returns The agent run result
 */


/**
 * Ensure all block types in a payload are uppercase
 * @param payload - The payload to check
 * @returns The modified payload with uppercase block types
 */
function ensureUppercaseBlockTypes(payload: any): any {
  // Create a deep copy of the payload
  const modifiedPayload = JSON.parse(JSON.stringify(payload));

  // Check if the payload has an agentConfig property
  if (modifiedPayload.agentConfig && modifiedPayload.agentConfig.blocks) {
    // Convert all block types to uppercase
    modifiedPayload.agentConfig.blocks = modifiedPayload.agentConfig.blocks.map((block: any) => {
      if (block.type) {
        // First, handle specific conversions
        if (block.type.toLowerCase() === 'when_run') {
          block.type = 'WHEN_RUN';
        } else if (block.type.toLowerCase() === 'indicator') {
          block.type = 'INDICATOR';
        } else if (block.type.toLowerCase() === 'price') {
          block.type = 'PRICE';
        } else if (block.type.toLowerCase() === 'fundamental') {
          block.type = 'FUNDAMENTAL';
        } else if (block.type.toLowerCase() === 'condition') {
          block.type = 'CONDITION';
        } else if (block.type.toLowerCase() === 'trigger') {
          block.type = 'TRIGGER';
        } else if (block.type.toLowerCase() === 'operator') {
          block.type = 'OPERATOR';
        } else if (block.type.toLowerCase() === 'bullish_confidence_boost') {
          block.type = 'BULLISH_CONFIDENCE_BOOST';
        } else if (block.type.toLowerCase() === 'bearish_confidence_boost') {
          block.type = 'BEARISH_CONFIDENCE_BOOST';
        } else {
          // For any other types, convert to uppercase
          block.type = block.type.toUpperCase();
        }

        // Remove any underscores if they exist
        block.type = block.type.replace(/_/g, '');
      }
      return block;
    });
  }

  // Also convert the stringified payload to catch any nested types
  const stringifiedPayload = JSON.stringify(modifiedPayload);
  const modifiedString = stringifiedPayload
    .replace(/"type":"when_run"/gi, '"type":"WHENRUN"')
    .replace(/"type":"indicator"/gi, '"type":"INDICATOR"')
    .replace(/"type":"price"/gi, '"type":"PRICE"')
    .replace(/"type":"fundamental"/gi, '"type":"FUNDAMENTAL"')
    .replace(/"type":"condition"/gi, '"type":"CONDITION"')
    .replace(/"type":"trigger"/gi, '"type":"TRIGGER"')
    .replace(/"type":"operator"/gi, '"type":"OPERATOR"')
    .replace(/"type":"bullish_confidence_boost"/gi, '"type":"BULLISHCONFIDENCEBOOST"')
    .replace(/"type":"bearish_confidence_boost"/gi, '"type":"BEARISHCONFIDENCEBOOST"');

  // Parse the modified string back to an object
  return JSON.parse(modifiedString);
}

/**
 * Convert block types to the format expected by the edge function
 * @param config - The agent configuration to convert
 * @returns The converted agent configuration
 */
function convertBlockTypes(config: Agent['configuration']): Agent['configuration'] {
  if (!config || !config.blocks) {
    return config;
  }

  // Create a deep copy of the configuration to avoid modifying the original
  const configCopy = JSON.parse(JSON.stringify(config));

  // Convert block types to uppercase with underscores
  configCopy.blocks = configCopy.blocks.map((block: any) => {
    // Create a new block object with the type converted to uppercase
    const newBlock = { ...block };

    // Convert the type to uppercase
    if (typeof block.type === 'string') {
      // Replace all occurrences of when_run with WHEN_RUN
      if (block.type.toLowerCase() === 'when_run') {
        newBlock.type = 'WHEN_RUN';
      } else if (block.type.toLowerCase() === 'indicator') {
        newBlock.type = 'INDICATOR';
      } else if (block.type.toLowerCase() === 'price') {
        newBlock.type = 'PRICE';
      } else if (block.type.toLowerCase() === 'fundamental') {
        newBlock.type = 'FUNDAMENTAL';
      } else if (block.type.toLowerCase() === 'condition') {
        newBlock.type = 'CONDITION';
      } else if (block.type.toLowerCase() === 'trigger') {
        newBlock.type = 'TRIGGER';
      } else if (block.type.toLowerCase() === 'operator') {
        newBlock.type = 'OPERATOR';
      } else if (block.type.toLowerCase() === 'bullish_confidence_boost') {
        newBlock.type = 'BULLISH_CONFIDENCE_BOOST';
      } else if (block.type.toLowerCase() === 'bearish_confidence_boost') {
        newBlock.type = 'BEARISH_CONFIDENCE_BOOST';
      }
    }

    return newBlock;
  });

  return configCopy;
}

/**
 * Validate an agent configuration
 * @param config - The agent configuration to validate
 * @returns An error message if invalid, or null if valid
 */
function validateAgentConfig(config: Agent['configuration']): string | null {
  if (!config) {
    return 'Agent configuration is missing';
  }

  if (!config.blocks || !Array.isArray(config.blocks) || config.blocks.length === 0) {
    return 'Agent configuration must have at least one block';
  }

  if (!config.entryBlockId) {
    return 'Agent configuration must have an entry block ID';
  }

  // Check if the entry block exists
  const entryBlockExists = config.blocks.some(block => block.id === config.entryBlockId);
  if (!entryBlockExists) {
    return 'Entry block ID does not match any block in the configuration';
  }

  // Check that all blocks have required properties
  for (const block of config.blocks) {
    if (!block.id) {
      return 'All blocks must have an ID';
    }

    if (!block.type) {
      return `Block ${block.id} is missing a type`;
    }

    if (!block.position || typeof block.position.x !== 'number' || typeof block.position.y !== 'number') {
      return `Block ${block.id} has an invalid position`;
    }
  }

  return null;
}

/**
 * Extract the response body from an edge function error
 * @param error - The error object
 * @returns The response body or null if not available
 */
function extractErrorResponseBody(error: any): any {
  try {
    // Check if the error has a response property
    if (error.response) {
      return error.response;
    }

    // Check if the error message contains a JSON string
    const jsonMatch = error.message.match(/\{.*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }

    // Check for specific error patterns
    const agentRunnerMatch = error.message.match(/Error in agent-runner: (.*)/);
    if (agentRunnerMatch && agentRunnerMatch[1]) {
      console.error('Extracted agent-runner error:', agentRunnerMatch[1]);
      return { error: agentRunnerMatch[1] };
    }

    // Check for HTTP error codes
    const httpErrorMatch = error.message.match(/(\d{3}) .*$/);
    if (httpErrorMatch && httpErrorMatch[1]) {
      const statusCode = httpErrorMatch[1];
      console.error('Extracted HTTP status code:', statusCode);
      return { statusCode, error: `HTTP error ${statusCode}` };
    }

    return null;
  } catch (e) {
    console.error('Error extracting response body:', e);
    return null;
  }
}

export async function runAgent(
  agentId: string,
  symbol: string,
  timeframe: string = 'day'
): Promise<AgentRun['result']> {
  try {
    // First, get the agent to ensure it exists and belongs to the user
    const agent = await getAgentById(agentId);

    if (!agent) {
      throw new Error(`Agent with ID ${agentId} not found`);
    }

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    // Validate the agent configuration
    const validationError = validateAgentConfig(agent.configuration);
    if (validationError) {
      throw new Error(`Invalid agent configuration: ${validationError}`);
    }

    // Convert block types to the format expected by the edge function
    const convertedConfig = convertBlockTypes(agent.configuration);

    console.log('Original block types:', agent.configuration.blocks.map(b => b.type));
    console.log('Converted block types:', convertedConfig.blocks.map(b => b.type));

    // Prepare the request payload with detailed information
    const requestPayload = {
      agentId,
      agentConfig: convertedConfig,
      symbol: symbol.toUpperCase(),
      timeframe,
      saveRun: true,
      userId: user.id,
      // Enable debug mode for development
      debug: process.env.NODE_ENV === 'development',
      // Include additional debugging information
      requestTime: new Date().toISOString(),
      clientInfo: {
        environment: process.env.NODE_ENV,
        userAgent: navigator.userAgent,
        language: navigator.language
      }
    };

    console.log('Agent runner request payload:', JSON.stringify(requestPayload, null, 2));

    // Clean up orphaned connections before sending to edge function
    const cleanupOrphanedConnections = (blocks: any[]) => {
      const blockIds = new Set(blocks.map(block => block.id));

      return blocks.map(block => {
        const cleanedBlock = { ...block };

        // Clean up inputConnections
        if (cleanedBlock.inputConnections) {
          cleanedBlock.inputConnections = cleanedBlock.inputConnections.filter((id: string) => blockIds.has(id));
        }

        // Clean up outputConnections
        if (cleanedBlock.outputConnections) {
          cleanedBlock.outputConnections = cleanedBlock.outputConnections.filter((id: string) => blockIds.has(id));
        }

        // Clean up trueConnection
        if (cleanedBlock.trueConnection && !blockIds.has(cleanedBlock.trueConnection)) {
          cleanedBlock.trueConnection = undefined;
        }

        // Clean up falseConnection
        if (cleanedBlock.falseConnection && !blockIds.has(cleanedBlock.falseConnection)) {
          cleanedBlock.falseConnection = undefined;
        }

        return cleanedBlock;
      });
    };

    // Clean up the agent configuration
    requestPayload.agentConfig.blocks = cleanupOrphanedConnections(requestPayload.agentConfig.blocks);

    // Ensure all block types are uppercase
    const modifiedPayload = ensureUppercaseBlockTypes(requestPayload);

    // Double-check that all block types are uppercase
    console.log('Modified payload block types:', modifiedPayload.agentConfig.blocks.map((b: any) => b.type));

    // Verify that all block types are uppercase
    const blockTypes = modifiedPayload.agentConfig.blocks.map((b: any) => b.type);
    const allUppercase = blockTypes.every((type: string) =>
      type === type.toUpperCase()
    );

    if (!allUppercase) {
      console.warn('Warning: Not all block types are uppercase. This may cause issues with the edge function.');
    }

    try {
      // Invoke the edge function with the modified configuration
      const { data, error } = await supabase.functions.invoke('agent-runner', {
        body: modifiedPayload
      });

      if (error) {
        // Try to extract the response body from the error
        const responseBody = extractErrorResponseBody(error);

        console.error('Edge function error details:', {
          message: error.message,
          name: error.name,
          stack: error.stack,
          code: (error as any).code,
          statusCode: (error as any).statusCode,
          details: (error as any).details,
          responseBody
        });

        // If we have a response body with an error message, use that
        if (responseBody && responseBody.error) {
          throw new Error(`Edge function error: ${responseBody.error}`);
        }

        // Provide more specific error messages based on the error
        if (error.message.includes('not found')) {
          throw new Error(`Agent with ID ${agentId} not found or you don't have permission to access it`);
        } else if (error.message.includes('authentication')) {
          throw new Error('Authentication error: Please log in again and try once more');
        } else if (error.message.includes('400')) {
          throw new Error('Invalid request: The agent configuration may be malformed. Check that all required fields are present and valid.');
        } else if (error.message.includes('500')) {
          throw new Error('Server error: The agent runner service encountered an internal error. Please try again later.');
        } else {
          throw new Error(`Failed to run agent: ${error.message}`);
        }
      }

      if (!data) {
        console.error('No data returned from agent runner');
        throw new Error('No data returned from agent runner');
      }

      // Validate the response data
      console.log('Agent runner response data:', data);

      // Check if data is an object
      if (typeof data !== 'object' || data === null) {
        console.error('Invalid response format (not an object):', data);
        throw new Error('Invalid response format from agent runner: not an object');
      }

      // Check if data has the required fields
      if (data.signal === undefined) {
        console.error('Invalid response format (missing signal):', data);
        throw new Error('Invalid response format from agent runner: missing signal');
      }

      if (data.confidence === undefined) {
        console.error('Invalid response format (missing confidence):', data);
        throw new Error('Invalid response format from agent runner: missing confidence');
      }

      // Convert the response to the expected format if needed
      const result = {
        signal: data.signal,
        confidence: data.confidence,
        reasoning: data.reasoning || '',
        metrics: data.metrics || {},
        executionPath: data.executionPath || [],
        executionTime: data.executionTime || 0,
        timestamp: data.timestamp || new Date().toISOString(),
        debugLogs: data.debugLogs || []
      };

      console.log('Agent runner response:', JSON.stringify(result, null, 2));
      return result;
    } catch (edgeError) {
      console.error('Error invoking edge function:', edgeError);

      // Rethrow with more context
      if (edgeError instanceof Error) {
        throw new Error(`Agent runner error: ${edgeError.message}`);
      } else {
        throw new Error('Unknown error occurred while running the agent');
      }
    }
  } catch (error) {
    console.error(`Error running agent with ID ${agentId}:`, error);
    throw error;
  }
}

/**
 * Run an agent with a custom configuration (without saving)
 * @param agentConfig - The agent configuration
 * @param symbol - The symbol to analyze
 * @param timeframe - The timeframe to analyze
 * @returns The agent run result
 */
export async function runCustomAgent(
  agentConfig: Agent['configuration'],
  symbol: string,
  timeframe: string = 'day'
): Promise<AgentRun['result']> {
  try {
    // Validate the agent configuration
    const validationError = validateAgentConfig(agentConfig);
    if (validationError) {
      throw new Error(`Invalid agent configuration: ${validationError}`);
    }

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    // Convert block types to the format expected by the edge function
    const convertedConfig = convertBlockTypes(agentConfig);

    console.log('Original block types:', agentConfig.blocks.map(b => b.type));
    console.log('Converted block types:', convertedConfig.blocks.map(b => b.type));

    // Prepare the request payload with detailed information
    const requestPayload = {
      agentConfig: convertedConfig,
      symbol: symbol.toUpperCase(),
      timeframe,
      saveRun: false,
      userId: user.id,
      // Enable debug mode for development
      debug: process.env.NODE_ENV === 'development',
      // Include additional debugging information
      requestTime: new Date().toISOString(),
      clientInfo: {
        environment: process.env.NODE_ENV,
        userAgent: navigator.userAgent,
        language: navigator.language
      }
    };

    console.log('Custom agent runner request payload:', JSON.stringify(requestPayload, null, 2));

    // Clean up orphaned connections before sending to edge function
    const cleanupOrphanedConnections = (blocks: any[]) => {
      const blockIds = new Set(blocks.map(block => block.id));

      return blocks.map(block => {
        const cleanedBlock = { ...block };

        // Clean up inputConnections
        if (cleanedBlock.inputConnections) {
          cleanedBlock.inputConnections = cleanedBlock.inputConnections.filter((id: string) => blockIds.has(id));
        }

        // Clean up outputConnections
        if (cleanedBlock.outputConnections) {
          cleanedBlock.outputConnections = cleanedBlock.outputConnections.filter((id: string) => blockIds.has(id));
        }

        // Clean up trueConnection
        if (cleanedBlock.trueConnection && !blockIds.has(cleanedBlock.trueConnection)) {
          cleanedBlock.trueConnection = undefined;
        }

        // Clean up falseConnection
        if (cleanedBlock.falseConnection && !blockIds.has(cleanedBlock.falseConnection)) {
          cleanedBlock.falseConnection = undefined;
        }

        return cleanedBlock;
      });
    };

    // Clean up the agent configuration
    requestPayload.agentConfig.blocks = cleanupOrphanedConnections(requestPayload.agentConfig.blocks);

    // Ensure all block types are uppercase
    const modifiedPayload = ensureUppercaseBlockTypes(requestPayload);

    // Double-check that all block types are uppercase
    console.log('Modified payload block types:', modifiedPayload.agentConfig.blocks.map((b: any) => b.type));

    // Verify that all block types are uppercase
    const blockTypes = modifiedPayload.agentConfig.blocks.map((b: any) => b.type);
    const allUppercase = blockTypes.every((type: string) =>
      type === type.toUpperCase()
    );

    if (!allUppercase) {
      console.warn('Warning: Not all block types are uppercase. This may cause issues with the edge function.');
    }

    try {
      // Invoke the edge function with the modified configuration
      const { data, error } = await supabase.functions.invoke('agent-runner', {
        body: modifiedPayload
      });

      if (error) {
        // Try to extract the response body from the error
        const responseBody = extractErrorResponseBody(error);

        console.error('Edge function error details:', {
          message: error.message,
          name: error.name,
          stack: error.stack,
          code: (error as any).code,
          statusCode: (error as any).statusCode,
          details: (error as any).details,
          responseBody
        });

        // If we have a response body with an error message, use that
        if (responseBody && responseBody.error) {
          throw new Error(`Edge function error: ${responseBody.error}`);
        }

        // Provide more specific error messages based on the error
        if (error.message.includes('authentication')) {
          throw new Error('Authentication error: Please log in again and try once more');
        } else if (error.message.includes('400')) {
          throw new Error('Invalid request: The agent configuration may be malformed. Check that all required fields are present and valid.');
        } else if (error.message.includes('500')) {
          throw new Error('Server error: The agent runner service encountered an internal error. Please try again later.');
        } else {
          throw new Error(`Failed to run agent: ${error.message}`);
        }
      }

      if (!data) {
        console.error('No data returned from agent runner');
        throw new Error('No data returned from agent runner');
      }

      // Validate the response data
      console.log('Custom agent runner response data:', data);

      // Check if data is an object
      if (typeof data !== 'object' || data === null) {
        console.error('Invalid response format (not an object):', data);
        throw new Error('Invalid response format from agent runner: not an object');
      }

      // Check if data has the required fields
      if (data.signal === undefined) {
        console.error('Invalid response format (missing signal):', data);
        throw new Error('Invalid response format from agent runner: missing signal');
      }

      if (data.confidence === undefined) {
        console.error('Invalid response format (missing confidence):', data);
        throw new Error('Invalid response format from agent runner: missing confidence');
      }

      // Convert the response to the expected format if needed
      const result = {
        signal: data.signal,
        confidence: data.confidence,
        reasoning: data.reasoning || '',
        metrics: data.metrics || {},
        executionPath: data.executionPath || [],
        executionTime: data.executionTime || 0,
        timestamp: data.timestamp || new Date().toISOString(),
        debugLogs: data.debugLogs || []
      };

      console.log('Custom agent runner response:', JSON.stringify(result, null, 2));
      return result;
    } catch (edgeError) {
      console.error('Error invoking edge function:', edgeError);

      // Rethrow with more context
      if (edgeError instanceof Error) {
        throw new Error(`Agent runner error: ${edgeError.message}`);
      } else {
        throw new Error('Unknown error occurred while running the agent');
      }
    }
  } catch (error) {
    console.error('Error running custom agent:', error);
    throw error;
  }
}

/**
 * Get agent runs for an agent
 * @param agentId - The agent ID
 * @param limit - Maximum number of runs to return
 * @returns Array of agent runs
 */
export async function getAgentRuns(
  agentId: string,
  limit: number = 10
): Promise<AgentRun[]> {
  try {
    const { data, error } = await supabase
      .from('agent_runs')
      .select('*')
      .eq('agent_id', agentId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error(`Error getting runs for agent with ID ${agentId}:`, error);
    throw error;
  }
}

/**
 * Get the latest agent run for a symbol
 * @param symbol - The symbol
 * @returns The latest agent run for the symbol
 */
export async function getLatestAgentRunForSymbol(
  symbol: string
): Promise<AgentRun | null> {
  try {
    const { data, error } = await supabase
      .from('agent_runs')
      .select('*')
      .eq('symbol', symbol)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error(`Error getting latest run for symbol ${symbol}:`, error);
    return null;
  }
}
