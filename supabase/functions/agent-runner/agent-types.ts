// Agent Types
// Type definitions for the agent system

// Block types
export enum BlockType {
  WHEN_RUN = 'WHEN_RUN', // Added WHEN_RUN block type
  INDICATOR = 'INDICATOR',
  PRICE = 'PRICE',
  FUNDAMENTAL = 'FUNDAMENTAL',
  CONDITION = 'CONDITION',
  TRIGGER = 'TRIGGER',
  OPERATOR = 'OPERATOR',
  BULLISH_CONFIDENCE_BOOST = 'BULLISH_CONFIDENCE_BOOST',
  BEARISH_CONFIDENCE_BOOST = 'BEARISH_CONFIDENCE_BOOST',
  AND = 'AND', // AND logic block
  OR = 'OR',   // OR logic block
  CANDLE_PATTERN = 'CANDLE_PATTERN' // Candle pattern detection block
}

// Base block interface
export interface Block {
  id: string;
  type: BlockType;
  position: { x: number; y: number };
}

// Technical indicator block
export interface IndicatorBlock extends Block {
  type: BlockType.INDICATOR;
  indicator: string; // 'rsi', 'macd', 'sma', etc.
  parameters: Record<string, any>; // Parameters specific to the indicator
  outputConnections: string[]; // IDs of connected blocks
}

// Price data block
export interface PriceBlock extends Block {
  type: BlockType.PRICE;
  dataPoint: string; // 'open', 'high', 'low', 'close', 'volume', etc.
  timeframe?: string; // Optional timeframe override
  lookback?: number; // How many periods to look back
  outputConnections: string[]; // IDs of connected blocks
}

// Fundamental data block
export interface FundamentalBlock extends Block {
  type: BlockType.FUNDAMENTAL;
  metric: string; // The specific financial metric to retrieve
  statement: 'balance_sheet' | 'income_statement' | 'cash_flow_statement' | 'comprehensive_income'; // Which financial statement
  period?: 'quarterly' | 'annual'; // Time period (defaults to most recent)
  parameters?: Record<string, any>; // Additional parameters
  outputConnections: string[]; // IDs of connected blocks
}

// Condition block (if/then/else)
export interface ConditionBlock extends Block {
  type: BlockType.CONDITION;
  operator: string; // '>', '<', '==', '>=', '<=', '!=', 'between', 'and', 'or', 'not'
  inputConnections: string[]; // IDs of input blocks
  compareValue?: number; // Value to compare against (not used for 'and', 'or', 'not')
  trueConnection?: string; // ID of block to execute if condition is true
  falseConnection?: string; // ID of block to execute if condition is false
}

// Trigger block (final output)
export interface TriggerBlock extends Block {
  type: BlockType.TRIGGER;
  signal: 'bullish' | 'bearish' | 'neutral';
  confidence: number; // 0-100
  inputConnections: string[]; // IDs of input blocks
}

// When Run block (entry point)
export interface WhenRunBlock extends Block {
  type: BlockType.WHEN_RUN;
  outputConnections: string[]; // IDs of connected blocks
}

// Operator block (mathematical operations)
export interface OperatorBlock extends Block {
  type: BlockType.OPERATOR;
  operation: string; // 'add', 'subtract', 'multiply', 'divide', 'average', etc.
  inputConnections: string[]; // IDs of input blocks
  outputConnections: string[]; // IDs of connected blocks
}

// Bullish Confidence Boost block (signal confidence adjustment)
export interface BullishConfidenceBoostBlock extends Block {
  type: BlockType.BULLISH_CONFIDENCE_BOOST;
  percentage: number; // Percentage to boost bullish confidence (e.g., 10 for +10%)
  inputConnections: string[]; // IDs of input blocks
  outputConnections: string[]; // IDs of connected blocks
}

// Bearish Confidence Boost block (signal confidence adjustment)
export interface BearishConfidenceBoostBlock extends Block {
  type: BlockType.BEARISH_CONFIDENCE_BOOST;
  percentage: number; // Percentage to boost bearish confidence (e.g., 10 for +10%)
  inputConnections: string[]; // IDs of input blocks
  outputConnections: string[]; // IDs of connected blocks
}

// AND logic block (logical AND operation)
export interface AndBlock extends Block {
  type: BlockType.AND;
  inputConnections: string[]; // IDs of input blocks (conditions)
  trueConnection?: string; // ID of block to execute if all inputs are true
  falseConnection?: string; // ID of block to execute if any input is false
}

// OR logic block (logical OR operation)
export interface OrBlock extends Block {
  type: BlockType.OR;
  inputConnections: string[]; // IDs of input blocks (conditions)
  trueConnection?: string; // ID of block to execute if any input is true
  falseConnection?: string; // ID of block to execute if all inputs are false
}

// Candle pattern detection block
export interface CandlePatternBlock extends Block {
  type: BlockType.CANDLE_PATTERN;
  pattern: string; // Pattern type: 'doji', 'hammer', 'shooting_star', 'engulfing_bullish', 'engulfing_bearish', 'any', etc.
  timeframe?: string; // Timeframe: 'day', 'hour', '15minute', '5minute', '1minute'
  inputConnections: string[]; // IDs of input blocks (price data)
  // For specific pattern mode (when pattern is not 'any')
  trueConnection?: string; // ID of block to execute if pattern is detected
  falseConnection?: string; // ID of block to execute if pattern is not detected
  // For any pattern mode (when pattern is 'any')
  bullishConnection?: string; // ID of block to execute for bullish patterns
  bearishConnection?: string; // ID of block to execute for bearish patterns
  neutralConnection?: string; // ID of block to execute for neutral patterns
}

// Union type for all block types
export type BlockUnion =
  | WhenRunBlock
  | IndicatorBlock
  | PriceBlock
  | FundamentalBlock
  | ConditionBlock
  | TriggerBlock
  | OperatorBlock
  | BullishConfidenceBoostBlock
  | BearishConfidenceBoostBlock
  | AndBlock
  | OrBlock
  | CandlePatternBlock;

// Agent configuration
export interface AgentConfig {
  name?: string; // Optional name for the agent
  description?: string;
  blocks: BlockUnion[];
  entryBlockId: string; // ID of the first block to execute
}

// Agent execution result
export interface AgentResult {
  signal: 'bullish' | 'bearish' | 'neutral';
  confidence: number; // 0-100
  reasoning: string;
  metrics: Record<string, any>; // Metrics calculated during execution
  executionPath: string[]; // IDs of blocks executed in order
  executionTime: number; // Time taken to execute in ms
  timestamp: string; // ISO timestamp of execution
}

// Financial statement data structure
export interface FinancialStatement {
  [metric: string]: {
    label: string;
    order: number;
    unit: string;
    value: number;
  } | number;
}

// Complete financials data structure
export interface FinancialsData {
  balance_sheet?: FinancialStatement;
  income_statement?: FinancialStatement;
  cash_flow_statement?: FinancialStatement;
  comprehensive_income?: FinancialStatement;
}

// Polygon data structure
export interface PolygonData {
  price: {
    current: number;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
    timestamp: number;
  };
  historical: {
    open: number[];
    high: number[];
    low: number[];
    close: number[];
    volume: number[];
    timestamp: number[];
  };
  indicators: Record<string, any>; // Calculated indicators
  fundamentals: Record<string, any>; // Legacy fundamental data
  financials?: FinancialsData; // Complete financial statements data
}
