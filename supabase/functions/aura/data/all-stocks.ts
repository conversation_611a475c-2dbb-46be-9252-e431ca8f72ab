// All Stocks List
// This file combines stocks from all major indices for comprehensive market scanning

import { SP500_STOCKS } from "./sp500-stocks.ts";
import { RUSSELL2000_STOCKS } from "./russell2000-stocks.ts";
import { NASDAQ_STOCKS } from "./nasdaq-stocks.ts";
import { NASDAQ100_STOCKS } from "./nasdaq100-stocks.ts";
import { COMPANY_TICKERS } from "./company-tickers.ts";

// Create a Set to remove duplicates
const allStocksSet = new Set([
  ...SP500_STOCKS,
  ...RUSSELL2000_STOCKS,
  ...NASDAQ_STOCKS,
  ...NASDAQ100_STOCKS,
  ...COMPANY_TICKERS // Add the tickers from company_tickers.json
]);

// Convert back to array
export const ALL_STOCKS = Array.from(allStocksSet);

// Note: This is a combined list from multiple indices and may contain duplicates.
// The Set is used to remove duplicates, but the list may still not be 100% accurate or up-to-date.
// For production use, consider fetching the current constituents from a reliable data source.

// The list now includes tickers from the SEC company_tickers.json file, which provides
// a more comprehensive coverage of stocks across various exchanges.
