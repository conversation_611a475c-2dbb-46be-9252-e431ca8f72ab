-- Create tables for the AI agent builder feature

-- Table to store user-created agents
CREATE TABLE IF NOT EXISTS public.agents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  configuration JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Table to store agent execution history
CREATE TABLE IF NOT EXISTS public.agent_runs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
  symbol TEXT NOT NULL,
  result JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS agents_user_id_idx ON public.agents(user_id);
CREATE INDEX IF NOT EXISTS agent_runs_agent_id_idx ON public.agent_runs(agent_id);
CREATE INDEX IF NOT EXISTS agent_runs_symbol_idx ON public.agent_runs(symbol);
CREATE INDEX IF NOT EXISTS agent_runs_created_at_idx ON public.agent_runs(created_at);

-- Add RLS policies
-- Users can only see their own agents
CREATE POLICY "Users can view their own agents"
  ON public.agents FOR SELECT
  USING (auth.uid() = user_id);

-- Users can only create agents for themselves
CREATE POLICY "Users can create their own agents"
  ON public.agents FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Users can only update their own agents
CREATE POLICY "Users can update their own agents"
  ON public.agents FOR UPDATE
  USING (auth.uid() = user_id);

-- Users can only delete their own agents
CREATE POLICY "Users can delete their own agents"
  ON public.agents FOR DELETE
  USING (auth.uid() = user_id);

-- Users can only see runs of their own agents
CREATE POLICY "Users can view runs of their own agents"
  ON public.agent_runs FOR SELECT
  USING (EXISTS (
    SELECT 1 FROM public.agents
    WHERE agents.id = agent_runs.agent_id
    AND agents.user_id = auth.uid()
  ));

-- Users can only create runs for their own agents
CREATE POLICY "Users can create runs for their own agents"
  ON public.agent_runs FOR INSERT
  WITH CHECK (EXISTS (
    SELECT 1 FROM public.agents
    WHERE agents.id = agent_runs.agent_id
    AND agents.user_id = auth.uid()
  ));

-- Users can only delete runs of their own agents
CREATE POLICY "Users can delete runs of their own agents"
  ON public.agent_runs FOR DELETE
  USING (EXISTS (
    SELECT 1 FROM public.agents
    WHERE agents.id = agent_runs.agent_id
    AND agents.user_id = auth.uid()
  ));

-- Enable RLS on the tables
ALTER TABLE public.agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_runs ENABLE ROW LEVEL SECURITY;

-- Create function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = timezone('utc'::text, now());
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update the updated_at column
CREATE TRIGGER update_agents_updated_at
BEFORE UPDATE ON public.agents
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Add comment to the tables
COMMENT ON TABLE public.agents IS 'Stores user-created trading agents';
COMMENT ON TABLE public.agent_runs IS 'Stores execution history of trading agents';
